
id: 66952
name: highfive-backend
environments:
    staging:
        domain: services.staging.highfive.vet
        memory: 1024
        cli-memory: 512
        cli-timeout: 900
        runtime: 'php-8.3:al2-arm'
        database: highfive-backend-staging
        database-proxy: true
        storage: highfive-backend-staging
        build:
            - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev'
            - php artisan event:cache
            - php artisan nova:publish
            - php artisan view:clear
        deploy:
            - php artisan migrate --seed --force
    production:
        domain: services.highfive.vet
        memory: 1024
        cli-memory: 512
        cli-timeout: 900
        runtime: 'php-8.3:al2-arm'
        database: highfive-backend-production
        storage: highfive-backend-production
        build:
            - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev'
            - php artisan event:cache
            - php artisan nova:publish
            - php artisan view:clear
