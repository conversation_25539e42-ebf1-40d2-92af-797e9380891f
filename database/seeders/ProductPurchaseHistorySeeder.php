<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

final class ProductPurchaseHistorySeeder extends Seeder
{
    public function run(): void
    {
        $clinic = Clinic::factory()->create();
        $product = ProductOffer::factory()->create();

        $endDate = Carbon::now()->endOfMonth();
        $startDate = $endDate->copy()->subMonths(11)->startOfMonth();
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            $orderCount = rand(2, 4);

            for ($i = 0; $i < $orderCount; $i++) {
                $order = Order::factory()->create([
                    'clinic_id' => $clinic->id,
                    'created_at' => $currentDate->copy()->addDays(rand(1, 28)),
                ]);

                $quantity = rand(1, 5);
                OrderItem::factory()->create([
                    'order_id' => $order->id,
                    'product_offer_id' => $product->id,
                    'quantity' => $quantity,
                    'price' => 100.00,
                ]);
            }

            $currentDate->addMonth();
        }

        $this->command->info('Test data created successfully!');
        $this->command->info("Clinic ID: {$clinic->id}");
        $this->command->info("Product ID: {$product->id}");
        $this->command->info("Data range: {$startDate->format('Y-m')} to {$endDate->format('Y-m')}");
    }
}
