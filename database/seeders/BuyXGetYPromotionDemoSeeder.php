<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Database\Seeder;

final class BuyXGetYPromotionDemoSeeder extends Seeder
{
    public function run(): void
    {
        $vendor = Vendor::factory()->create([
            'name' => 'Demo Vendor',
        ]);

        $gpo = GpoAccount::factory()->create([
            'name' => 'Demo GPO',
        ]);

        $product = Product::factory()->create([
            'name' => 'Demo Product',
        ]);

        $productOffer = ProductOffer::factory()->create([
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'name' => 'Demo Product Offer',
        ]);

        $promotion = Promotion::create([
            'name' => 'Buy X Get Y Demo Promotion',
            'type' => PromotionType::BuyXGetY,
            'description' => 'Buy 3, get 3 free demo promotion',
            'vendor_id' => $vendor->id,
            'priority' => 1,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addMonth(),
            'status' => PromotionStatus::Active,
            'promotionable_type' => get_class($gpo),
            'promotionable_id' => $gpo->id,
        ]);

        // Attach product offer to promotion
        $promotion->productOffers()->attach($productOffer->id);

        // Create rule with condition and action
        $rule = $promotion->rules()->create(['priority' => 1]);

        $rule->conditions()->create([
            'type' => ConditionType::MinimumQuantity,
            'config' => [
                'quantity' => 3,
            ],
        ]);

        $rule->actions()->create([
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'quantity' => 1,
            ],
        ]);
    }
}
