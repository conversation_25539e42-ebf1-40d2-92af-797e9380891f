<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Vendor;
use Illuminate\Database\Seeder;

final class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach (config('highfive.vendors') as $data) {
            $contacts = $data['account_receivable_contacts'];
            unset($data['account_receivable_contacts']);

            $terms = $data['shipping_terms'];
            unset($data['shipping_terms']);

            $vendor = Vendor::updateOrCreate(['key' => $data['key']], $data);

            foreach ($contacts as $contact) {
                $vendor->accountReceivableContacts()->updateOrCreate(['email' => $contact['email']], $contact);
            }

            if ($vendor->shippingTerms()->doesntExist() && $terms) {
                $vendor->shippingTerms()->create($terms);
            }
        }
    }
}
