<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\ProductStockStatus;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
final class ProductOfferFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $adjectives = [
            'Sterile',
            'Advanced',
            'Comfort',
            'Rapid',
            'Eco-Friendly',
            'Precision',
            'Professional',
            'Ultra',
            'Safe',
            'Reliable',
        ];

        $products = [
            'Gauze Pads',
            'Thermometer',
            'Syringe Kit',
            'Diagnostic Kit',
            'Antiseptic Solution',
            'Pet Shampoo',
            'Suture Material',
            'Wound Spray',
            'Exam Gloves',
            'Pet Carrier',
        ];

        return [
            'name' => fake()->randomElement($adjectives).' '.fake()->randomElement($products),
            'vendor_id' => Vendor::factory()->enabled(),
            'vendor_sku' => fake()->unique()->numerify('SKU-####'),
            'price' => fake()->randomNumber(5),
            'stock_status' => fake()->randomElement(ProductStockStatus::cases()),
            'keywords' => fake()->words(5),
            'product_id' => Product::factory()->create(),
            'last_synced_at' => now(),
        ];
    }
}
