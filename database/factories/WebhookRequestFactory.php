<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\WebhookRequestStatus;
use App\Models\Vendor;
use App\Models\WebhookRequest;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<WebhookRequest>
 */
final class WebhookRequestFactory extends Factory
{
    protected $model = WebhookRequest::class;

    public function definition(): array
    {
        return [
            'vendor_id' => Vendor::factory(),
            'method' => $this->faker->randomElement(['POST', 'GET', 'PUT', 'DELETE']),
            'slug' => $this->faker->randomElement(['order.created', 'order.updated', 'order.cancelled']),
            'headers' => [
                'X-Webhook-Signature' => [$this->faker->sha256],
                'Content-Type' => ['application/json'],
                'User-Agent' => [$this->faker->userAgent],
            ],
            'payload' => [
                'order_id' => $this->faker->uuid,
                'status' => $this->faker->randomElement(['pending', 'processing', 'completed']),
                'timestamp' => $this->faker->iso8601,
            ],
            'status' => $this->faker->randomElement(WebhookRequestStatus::cases()),
            'error_message' => fn (array $attributes) => $attributes['status'] === WebhookRequestStatus::Failed ? $this->faker->sentence : null,
        ];
    }

    public function failed(): self
    {
        return $this->state(fn (array $attributes) => [
            'status' => WebhookRequestStatus::Failed,
            'error_message' => $this->faker->sentence,
        ]);
    }

    public function completed(): self
    {
        return $this->state(fn (array $attributes) => [
            'status' => WebhookRequestStatus::Completed,
            'error_message' => null,
        ]);
    }

    public function pending(): self
    {
        return $this->state(fn (array $attributes) => [
            'status' => WebhookRequestStatus::Pending,
            'error_message' => null,
        ]);
    }
}
