<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Cart;
use App\Models\ProductOffer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CartItem>
 */
final class CartItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cart_id' => Cart::factory(),
            'product_offer_id' => ProductOffer::factory(),
            'price' => fake()->numberBetween(1000, 10000),
            'quantity' => fake()->numberBetween(1, 10),
        ];
    }
}
