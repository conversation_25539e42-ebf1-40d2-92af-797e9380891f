<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Order;
use App\Models\OrderPromotion;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Database\Eloquent\Factories\Factory;

final class OrderPromotionFactory extends Factory
{
    protected $model = OrderPromotion::class;

    public function definition(): array
    {
        return [
            'order_id' => Order::factory(),
            'promotion_id' => Promotion::factory(),
            'triggering_items' => [
                [
                    'product_offer_id' => $this->faker->uuid(),
                    'product_id' => $this->faker->uuid(),
                    'product_name' => $this->faker->words(2, true),
                    'quantity' => $this->faker->numberBetween(1, 10),
                ],
            ],
            'applied_rules' => [
                [
                    'rule_id' => $this->faker->uuid(),
                    'priority' => 1,
                    'conditions' => [
                        [
                            'id' => $this->faker->uuid(),
                            'type' => ConditionType::MinimumQuantity->value,
                            'description' => 'Minimum quantity: 5',
                            'config' => ['quantity' => 5],
                        ],
                    ],
                    'actions' => [
                        [
                            'id' => $this->faker->uuid(),
                            'type' => ActionType::GiveFreeProduct->value,
                            'description' => 'Give free product',
                            'config' => ['quantity' => 1],
                        ],
                    ],
                ],
            ],
            'applied_benefits' => [
                [
                    'type' => 'free_product',
                    'product_offer_id' => $this->faker->uuid(),
                    'product_name' => $this->faker->words(2, true),
                    'quantity' => 1,
                    'message' => 'Buy 5, get 1 free!',
                ],
            ],
        ];
    }

    /**
     * Create a Buy X Get Y promotion
     */
    public function buyXGetY(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'applied_benefits' => [
                    [
                        'type' => 'free_product',
                        'product_offer_id' => $this->faker->uuid(),
                        'product_name' => $this->faker->words(2, true),
                        'quantity' => 1,
                        'message' => 'Buy 5, get 1 free!',
                    ],
                ],
            ];
        });
    }

    /**
     * Create a rebate promotion
     */
    public function rebate(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'applied_benefits' => [
                    [
                        'type' => 'rebate',
                        'percentage' => 5.0,
                        'description' => '5% rebate on qualifying purchases',
                    ],
                ],
            ];
        });
    }

    /**
     * Create a discount promotion
     */
    public function discount(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'applied_benefits' => [
                    [
                        'type' => 'discount',
                        'amount' => 25.50,
                        'percentage' => 10.0,
                        'description' => 'Discount: $25.50',
                    ],
                ],
            ];
        });
    }
}
