<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use App\Modules\CatalogSync\Models\CatalogSyncBatch;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\CatalogSync\Models\CatalogSyncBatch>
 */
final class CatalogSyncBatchFactory extends Factory
{
    protected $model = CatalogSyncBatch::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'catalog_sync_task_id' => CatalogSyncTask::factory(),
            'status' => CatalogSyncBatchStatus::Pending,
            'message' => [],
        ];
    }
}
