<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Promotion\Models;

use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionRule;
use Illuminate\Database\Eloquent\Factories\Factory;

final class PromotionRuleFactory extends Factory
{
    protected $model = PromotionRule::class;

    public function definition(): array
    {
        return [
            'promotion_id' => Promotion::factory(),
            'priority' => $this->faker->numberBetween(1, 10),
        ];
    }
}
