<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Promotion\Models;

use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Models\PromotionAction;
use App\Modules\Promotion\Models\PromotionRule;
use Illuminate\Database\Eloquent\Factories\Factory;

final class PromotionActionFactory extends Factory
{
    protected $model = PromotionAction::class;

    public function definition(): array
    {
        return [
            'promotion_rule_id' => PromotionRule::factory(),
            'type' => ActionType::UpdateRebateEstimate,
            'config' => [
                'rebate_percent' => $this->faker->numberBetween(1, 20),
            ],
        ];
    }

    public function giveFreeProduct(): self
    {
        return $this->state(fn (array $attributes) => [
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'free_product_id' => $this->faker->uuid(),
                'free_quantity' => $this->faker->numberBetween(1, 3),
                'original_price' => $this->faker->randomFloat(2, 10, 100),
            ],
        ]);
    }
}
