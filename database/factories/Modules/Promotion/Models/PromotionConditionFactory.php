<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Promotion\Models;

use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Models\PromotionCondition;
use App\Modules\Promotion\Models\PromotionRule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Promotion\Models\PromotionCondition>
 */
final class PromotionConditionFactory extends Factory
{
    protected $model = PromotionCondition::class;

    public function definition(): array
    {
        return [
            'promotion_rule_id' => PromotionRule::factory(),
            'type' => $this->faker->randomElement([
                ConditionType::MinimumQuantity->value,
                ConditionType::MinimumSpendAmount->value,
            ]),
            'config' => [
                'quantity' => $this->faker->numberBetween(1, 10),
                'minimum_spend_amount' => $this->faker->numberBetween(1000, 10000),
            ],
        ];
    }

    public function minimumQuantity(int $quantity = 5): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => ConditionType::MinimumQuantity->value,
            'config' => [
                'quantity' => $quantity,
            ],
        ]);
    }

    public function minimumSpendAmount(int $amount = 5000): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => ConditionType::MinimumSpendAmount->value,
            'config' => ['minimum_spend_amount' => $amount],
        ]);
    }
}
