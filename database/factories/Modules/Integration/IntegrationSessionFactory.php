<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Integration;

use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Integration\Models\IntegrationSession;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

final class IntegrationSessionFactory extends Factory
{
    protected $model = IntegrationSession::class;

    public function definition(): array
    {
        return [
            'id' => (string) Str::uuid(),
            'integration_connection_id' => IntegrationConnection::factory(),
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => 'started',
            'started_at' => now(),
            'finished_at' => null,
        ];
    }
}
