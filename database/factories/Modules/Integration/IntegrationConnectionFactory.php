<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Integration;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

final class IntegrationConnectionFactory extends Factory
{
    protected $model = IntegrationConnection::class;

    public function definition(): array
    {
        return [
            'id' => (string) Str::uuid(),
            'vendor_id' => Vendor::factory(),
            'clinic_id' => Clinic::factory(),
            'status' => 'connecting',
            'credentials' => ['api_key' => 'dummy'],
        ];
    }
}
