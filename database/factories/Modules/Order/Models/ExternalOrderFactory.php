<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Order\Models;

use App\Models\SubOrder;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ExternalOrder>
 */
final class ExternalOrderFactory extends Factory
{
    protected $model = ExternalOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'sub_order_id' => SubOrder::factory(),
            'external_order_id' => $this->faker->uuid(),
            'status' => $this->faker->word(),
        ];
    }
}
