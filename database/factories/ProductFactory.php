<?php

declare(strict_types=1);

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
final class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        $categories = [
            'Analgesics',
            'Antibiotics',
            'Antivirals',
            'Antifungals',
            'Antihistamines',
            'Cardiovascular',
            'Gastrointestinal',
            'Endocrine',
            'Psychotropics',
            'Respiratory',
        ];

        return [
            'name' => fake()->name(),
            'manufacturer_sku' => fake()->unique()->numerify('SKU-####'),
            'category' => fake()->randomElement($categories),
            'image_url' => fake()->imageUrl(),
        ];
    }
}
