<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\ExpenseCategory;
use App\Enums\VendorType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vendor>
 */
final class VendorFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key' => fake()->unique()->regexify('[A-Z0-9]{10}'),
            'name' => fake()->name(),
            'slug' => fake()->slug(),
            'image_path' => fake()->filePath(),
            'type' => fake()->randomElement(VendorType::values()),
            'expense_category' => fake()->randomElement(ExpenseCategory::values()),
            'purchase_order_email' => fake()->email(),
            'new_account_email' => fake()->email(),
            'phone_number' => fake()->phoneNumber(),
            'credentials_schema' => [],
        ];
    }

    /**
     * Indicate that the model's is_enabled is true.
     */
    public function enabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_enabled' => true,
        ]);
    }
}
