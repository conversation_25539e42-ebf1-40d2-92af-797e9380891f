<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\CatalogSync\Models\CatalogSyncTask>
 */
final class CatalogSyncTaskFactory extends Factory
{
    protected $model = CatalogSyncTask::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'status' => CatalogSyncTaskStatus::Pending,
        ];
    }
}
