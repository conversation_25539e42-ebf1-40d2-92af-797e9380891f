<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Clinic>
 */
final class ClinicFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_account_id' => ClinicAccount::factory(),
            'name' => fake()->company(),
            'business_tax_id' => fake()->unique()->ein(),
            'phone_number' => fake()->phoneNumber(),
            'is_billing_same_as_shipping_address' => true,
        ];
    }
}
