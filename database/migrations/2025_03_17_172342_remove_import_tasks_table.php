<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('import_tasks')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->dropForeign(['import_task_id']);
                $table->dropColumn('import_task_id');
            });

            Schema::table('sub_orders', function (Blueprint $table) {
                $table->dropForeign(['import_task_id']);
                $table->dropColumn('import_task_id');
            });

            Schema::table('orders', function (Blueprint $table) {
                $table->dropForeign(['import_task_id']);
                $table->dropColumn('import_task_id');
            });

            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['import_task_id']);
                $table->dropColumn('import_task_id');
            });

            Schema::drop('import_tasks');
        }

        Schema::dropIfExists('import_task_records');
    }
};
