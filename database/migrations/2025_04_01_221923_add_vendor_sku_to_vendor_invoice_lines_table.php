<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('vendor_invoice_lines', function (Blueprint $table) {
            $table->string('vendor_sku')->nullable()->after('external_id');
        });
    }

    public function down(): void
    {
        Schema::table('vendor_invoice_lines', function (Blueprint $table) {
            $table->dropColumn('vendor_sku');
        });
    }
};
