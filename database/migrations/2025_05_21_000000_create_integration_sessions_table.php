<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('integration_sessions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_connection_id');
            $table->foreign('integration_connection_id')->references('id')->on('integration_connections');
            $table->enum('integration_point', [
                'sync_product_catalog',
                'place_orders',
                'sync_order_status',
                'sync_order_shipments',
                'sync_order_invoices',
                'reconcile_order_lines',
            ]);
            $table->enum('status', ['started', 'succeeded', 'failed']);
            $table->timestamp('started_at');
            $table->timestamp('finished_at')->nullable();
            $table->timestamps();
        });
    }
};
