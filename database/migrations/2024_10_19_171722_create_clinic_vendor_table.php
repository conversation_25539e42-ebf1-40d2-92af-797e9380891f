<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_vendor', function (Blueprint $table) {
            $table->foreignIdFor(Clinic::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Vendor::class)->constrained()->cascadeOnDelete();
            $table->string('customer_number')->nullable();
            $table->text('credentials')->nullable();
            $table->enum('status', [
                'NOT_CONNECTED',
                'CONNECTING',
                'CONNECTED',
                'ERROR',
            ])->default('NOT_CONNECTED');
            $table->string('error_message')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->unique(['clinic_id', 'vendor_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_vendor');
    }
};
