<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('primary_category')->nullable();
            $table->string('secondary_category')->nullable();
            $table->boolean('is_hazardous')->default(false);
            $table->boolean('requires_prescription')->default(false);
            $table->boolean('requires_cold_shipping')->default(false);
            $table->boolean('is_controlled_substance')->default(false);
            $table->boolean('requires_pedigree')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('primary_category');
            $table->dropColumn('secondary_category');
            $table->dropColumn('is_hazardous');
            $table->dropColumn('requires_prescription');
            $table->dropColumn('requires_cold_shipping');
            $table->dropColumn('is_controlled_substance');
            $table->dropColumn('requires_pedigree');
        });
    }
};
