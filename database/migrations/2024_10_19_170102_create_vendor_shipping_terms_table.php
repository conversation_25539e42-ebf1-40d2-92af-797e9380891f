<?php

declare(strict_types=1);

use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_shipping_terms', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(Vendor::class)->unique()->constrained()->cascadeOnDelete();
            $table->string('cutoff_time')->nullable();
            $table->unsignedInteger('free_shipping_threshold')->default(0);
            $table->unsignedInteger('shipping_rate')->default(0);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_shipping_terms');
    }
};
