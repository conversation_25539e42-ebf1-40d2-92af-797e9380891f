<?php

declare(strict_types=1);

use App\Enums\ExpenseCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('key')->unique();
            $table->string('name');
            $table->string('image_path')->nullable();
            $table->enum('type', [
                'distributor',
                'manufacturer',
            ]);
            $table->enum('expense_category', ExpenseCategory::values());
            $table->string('purchase_order_email')->nullable();
            $table->string('new_account_email')->nullable();
            $table->string('phone_number')->nullable();
            $table->json('credentials_schema')->nullable();
            $table->boolean('is_enabled')->default(false);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
