<?php

declare(strict_types=1);

use App\Modules\Account\Enums\ClinicAccountPermission;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\Permission;
use App\Modules\Account\Models\Role;
use App\Modules\Gpo\Enums\GpoAccountRole;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->createClinicPermissions();

        $this->createClinicAccountRoles();

        $this->createGpoAccountRoles();
    }

    private function createClinicPermissions(): void
    {
        Permission::create(['name' => ClinicAccountPermission::ClinicFullAccess]);
        Permission::create(['name' => ClinicAccountPermission::ClinicOrdersFullAccess]);
        Permission::create(['name' => ClinicAccountPermission::ViewClinicOrders]);
    }

    private function createClinicAccountRoles(): void
    {
        $owner = Role::create(['name' => ClinicAccountRole::Owner]);
        $owner->givePermissionTo(ClinicAccountPermission::ClinicFullAccess);

        $admin = Role::create(['name' => ClinicAccountRole::Admin]);
        $admin->givePermissionTo(ClinicAccountPermission::ClinicFullAccess);

        $manager = Role::create(['name' => ClinicAccountRole::Manager]);
        $manager->givePermissionTo(ClinicAccountPermission::ClinicOrdersFullAccess);
    }

    private function createGpoAccountRoles(): void
    {
        $owner = Role::create(['name' => GpoAccountRole::Owner]);
    }
};
