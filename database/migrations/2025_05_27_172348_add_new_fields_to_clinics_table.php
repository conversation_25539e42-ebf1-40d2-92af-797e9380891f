<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->string('species_focus')->nullable();
            $table->jsonb('practice_types')->nullable();
            $table->integer('fulltime_dvm_count')->nullable();
            $table->integer('exam_rooms_count')->nullable();
            $table->string('primary_shopping_preference')->nullable();
            $table->jsonb('secondary_shopping_preferences')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->dropColumn('species_focus');
            $table->dropColumn('practice_types');
            $table->dropColumn('fulltime_dvm_count');
            $table->dropColumn('exam_rooms_count');
            $table->dropColumn('primary_shopping_preference');
            $table->dropColumn('secondary_shopping_preferences');
        });
    }
};
