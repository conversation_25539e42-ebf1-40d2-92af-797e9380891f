<?php

declare(strict_types=1);

use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Update promotion types from 'bogo' to 'buy_x_get_y'
        DB::table('promotions')
            ->where('type', 'bogo')
            ->update(['type' => PromotionType::BuyXGetY->value]);

        // Update promotions.type constraint
        DB::statement('ALTER TABLE promotions DROP CONSTRAINT IF EXISTS promotions_type_check');
        DB::statement("ALTER TABLE promotions ADD CONSTRAINT promotions_type_check CHECK (type IN ('".PromotionType::Rebate->value."', '".PromotionType::BuyXGetY->value."'))");

        // Update promotion_conditions.type constraint
        DB::statement('ALTER TABLE promotion_conditions DROP CONSTRAINT IF EXISTS promotion_conditions_type_check');
        DB::statement("ALTER TABLE promotion_conditions ADD CONSTRAINT promotion_conditions_type_check CHECK (type IN ('".ConditionType::MinimumSpendAmount->value."', '".ConditionType::MinimumYearOverYearSpendGrowthPercent->value."', '".ConditionType::MinimumQuantity->value."'))");

        // Update promotion_actions.type constraint
        DB::statement('ALTER TABLE promotion_actions DROP CONSTRAINT IF EXISTS promotion_actions_type_check');
        DB::statement("ALTER TABLE promotion_actions ADD CONSTRAINT promotion_actions_type_check CHECK (type IN ('".ActionType::UpdateRebateEstimate->value."', '".ActionType::GiveFreeProduct->value."'))");
    }
};
