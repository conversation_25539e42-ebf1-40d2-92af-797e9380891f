<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('integration_connections', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('clinic_id');
            $table->uuid('vendor_id');
            $table->text('credentials');
            $table->enum('status', [
                'disconnected',
                'connecting',
                'connected',
            ]);
            $table->string('status_reason')->nullable();
            $table->timestamps();

            $table->foreign('clinic_id')->references('id')->on('clinics');
            $table->foreign('vendor_id')->references('id')->on('vendors');

            $table->unique(['clinic_id', 'vendor_id']);
        });
    }
};
