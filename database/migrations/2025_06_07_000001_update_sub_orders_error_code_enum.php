<?php

declare(strict_types=1);

use App\Modules\Order\Enums\VendorError;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Drop the old constraint if it exists
        DB::statement('ALTER TABLE sub_orders DROP CONSTRAINT IF EXISTS sub_orders_error_code_check');

        // Get allowed values from the enum
        $allowed = array_map(fn ($v) => "'{$v}'", VendorError::values());
        $allowedList = implode(',', $allowed);

        // Add the new check constraint
        DB::statement("ALTER TABLE sub_orders ADD CONSTRAINT sub_orders_error_code_check CHECK (error_code IN ($allowedList))");
    }
};
