<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->text('description')->nullable();
            $table->string('image_url')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('manufacturer_sku')->nullable();
            $table->string('category')->nullable();
            $table->boolean('is_hazardous')->default(false);
            $table->boolean('requires_prescription')->default(false);
            $table->boolean('requires_cold_shipping')->default(false);
            $table->boolean('is_controlled_substance')->default(false);
            $table->boolean('requires_pedigree')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'description',
                'image_url',
                'manufacturer',
                'manufacturer_sku',
                'category',
                'is_hazardous',
                'requires_prescription',
                'requires_cold_shipping',
                'is_controlled_substance',
                'requires_pedigree',
            ]);
        });
    }
};
