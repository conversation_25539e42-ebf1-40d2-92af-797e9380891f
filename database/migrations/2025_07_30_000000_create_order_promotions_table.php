<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('order_promotions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('order_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('promotion_id')->constrained('promotions');

            // Triggering items (what triggered the promotion)
            $table->json('triggering_items')->nullable();

            // Applied rules and conditions
            $table->json('applied_rules')->nullable();

            // Benefits applied
            $table->json('applied_benefits')->nullable();

            $table->timestamps();

            $table->index(['order_id', 'promotion_id']);
            $table->unique(['order_id', 'promotion_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('order_promotions');
    }
};
