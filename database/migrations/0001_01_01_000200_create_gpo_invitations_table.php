<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('gpo_invitations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('gpo_account_id')->constrained('accounts')->cascadeOnDelete();
            $table->string('email')->index();
            $table->string('gpo_membership_number')->nullable();
            $table->foreignUuid('clinic_account_id')->nullable()->constrained('accounts')->cascadeOnDelete();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('expires_at');
            $table->timestamps();
        });
    }
};
