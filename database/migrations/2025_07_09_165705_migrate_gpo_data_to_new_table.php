<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::transaction(function () {
            DB::statement("
                INSERT INTO gpo_accounts (id, name, created_at, updated_at)
                SELECT id, name, created_at, updated_at
                FROM accounts
                WHERE type = 'App\Modules\Account\Models\GpoAccount'
            ");

            DB::statement("
                UPDATE promotions
                SET promotionable_type = 'App\Modules\Gpo\Models\GpoAccount'
                WHERE promotionable_type = 'App\Modules\Account\Models\GpoAccount'
            ");

            // using dropForeign([]) resolves to suffixing foreign key with _foreign, which is not the case in production/staging
            if (app()->environment('testing') === false) {
                Schema::table('accounts', function (Blueprint $table) {
                    $table->dropForeign('accounts_gpo_account_id_fkey');
                });

                Schema::table('accounts', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_account_details', function (Blueprint $table) {
                    $table->dropForeign('gpo_account_details_gpo_account_id_fkey');
                });

                Schema::table('gpo_account_details', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_recommended_products', function (Blueprint $table) {
                    $table->dropForeign('gpo_recommended_products_gpo_account_id_fkey');
                });

                Schema::table('gpo_recommended_products', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
                    $table->dropForeign('gpo_recommended_vendors_gpo_account_id_fkey');
                });

                Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_invitations', function (Blueprint $table) {
                    $table->dropForeign('gpo_invitations_gpo_account_id_fkey');
                });

                Schema::table('gpo_invitations', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });
            } else {
                Schema::table('accounts', function (Blueprint $table) {
                    $table->dropForeign(['gpo_account_id']);
                });

                Schema::table('accounts', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_account_details', function (Blueprint $table) {
                    $table->dropForeign(['gpo_account_id']);
                });

                Schema::table('gpo_account_details', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_recommended_products', function (Blueprint $table) {
                    $table->dropForeign(['gpo_account_id']);
                });

                Schema::table('gpo_recommended_products', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
                    $table->dropForeign(['gpo_account_id']);
                });

                Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

                Schema::table('gpo_invitations', function (Blueprint $table) {
                    $table->dropForeign(['gpo_account_id']);
                });

                Schema::table('gpo_invitations', function (Blueprint $table) {
                    $table->foreign('gpo_account_id')->references('id')->on('gpo_accounts');
                });

            }
        });
    }
};
