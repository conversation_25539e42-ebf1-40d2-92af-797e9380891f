<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinic_product', function (Blueprint $table) {
            $table->integer('price')->nullable()->change();
            $table->timestamp('favorited_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinic_product', function (Blueprint $table) {
            $table->integer('price')->nullable(false)->change();
            $table->dropColumn('favorited_at');
        });
    }
};
