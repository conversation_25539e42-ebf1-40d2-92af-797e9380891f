<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vendor_invoices', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('external_order_id');
            $table->string('invoice_number');
            $table->string('file_path')->nullable();
            $table->string('currency_code', 3);
            $table->integer('tax_fee');
            $table->integer('shipping_fee');
            $table->integer('subtotal');
            $table->integer('total');
            $table->timestamp('issued_at');
            $table->timestamps();

            $table->foreign('external_order_id')->references('id')->on('external_orders');
            $table->unique(['external_order_id', 'invoice_number']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vendor_invoices');
    }
};
