<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('catalog_sync_tasks', function (Blueprint $table) {
            $table->dropForeign(['vendor_id']);
            $table->dropForeign(['clinic_id']);

            $table->dropColumn(['vendor_id', 'clinic_id']);

            $table->uuid('integration_connection_id');

            $table->foreign('integration_connection_id')->references('id')->on('integration_connections');
        });
    }
};
