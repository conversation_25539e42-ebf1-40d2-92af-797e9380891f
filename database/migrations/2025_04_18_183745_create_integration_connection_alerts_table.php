<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('integration_connection_alerts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_connection_id');
            $table->enum('type', [
                'info',
                'error',
            ]);
            $table->text('message');
            $table->timestamps();

            $table->foreign('integration_connection_id')->references('id')->on('integration_connections');

            $table->unique(['integration_connection_id']);
        });
    }
};
