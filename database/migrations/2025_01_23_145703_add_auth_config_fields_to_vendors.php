<?php

declare(strict_types=1);

use App\Enums\VendorAuthenticationKind;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->enum('authentication_kind', VendorAuthenticationKind::values())->default(VendorAuthenticationKind::Basic);
            $table->json('authentication_configuration')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->dropColumn('authentication_kind');
            $table->dropColumn('authentication_configuration');
        });
    }
};
