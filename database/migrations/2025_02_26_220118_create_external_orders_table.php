<?php

declare(strict_types=1);

use App\Models\SubOrder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('external_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(SubOrder::class)->constrained()->cascadeOnDelete();
            $table->string('external_order_id');
            $table->string('status')->nullable();
            $table->timestamps();

            $table->unique(['sub_order_id', 'external_order_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('external_orders');
    }
};
