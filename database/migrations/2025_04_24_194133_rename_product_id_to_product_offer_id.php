<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_offer_attributes', function ($table) {
            $table->renameColumn('product_id', 'product_offer_id');
        });
        Schema::table('gpo_recommended_products', function ($table) {
            $table->renameColumn('product_id', 'product_offer_id');
        });
        Schema::table('cart_items', function ($table) {
            $table->renameColumn('product_id', 'product_offer_id');
        });
        Schema::table('order_items', function ($table) {
            $table->renameColumn('product_id', 'product_offer_id');
        });
        Schema::table('product_offer_search_terms', function ($table) {
            $table->renameColumn('product_id', 'product_offer_id');
        });
        Schema::table('clinic_product_offer', function ($table) {
            $table->renameColumn('product_id', 'product_offer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_offer_attributes', function ($table) {
            $table->renameColumn('product_offer_id', 'product_id');
        });
        Schema::table('gpo_recommended_products', function ($table) {
            $table->renameColumn('product_offer_id', 'product_id');
        });
        Schema::table('cart_items', function ($table) {
            $table->renameColumn('product_offer_id', 'product_id');
        });
        Schema::table('order_items', function ($table) {
            $table->renameColumn('product_offer_id', 'product_id');
        });
        Schema::table('product_offer_search_terms', function ($table) {
            $table->renameColumn('product_offer_id', 'product_id');
        });
        Schema::table('clinic_product_offer', function ($table) {
            $table->renameColumn('product_offer_id', 'product_id');
        });
    }
};
