<?php

declare(strict_types=1);

use App\Modules\Promotion\Enums\ActionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Drop the old constraint with the incorrect name
        DB::statement('ALTER TABLE promotion_actions DROP CONSTRAINT IF EXISTS promotion_rewards_type_check');
        
        // Drop any existing promotion_actions_type_check constraint to avoid conflicts
        DB::statement('ALTER TABLE promotion_actions DROP CONSTRAINT IF EXISTS promotion_actions_type_check');
        
        // Add the correct constraint with the proper name
        DB::statement("ALTER TABLE promotion_actions ADD CONSTRAINT promotion_actions_type_check CHECK (type IN ('".ActionType::UpdateRebateEstimate->value."', '".ActionType::GiveFreeProduct->value."'))");
    }

    public function down(): void
    {
        // Drop the correct constraint
        DB::statement('ALTER TABLE promotion_actions DROP CONSTRAINT IF EXISTS promotion_actions_type_check');
        
        // Restore the old constraint (though this is probably not needed)
        DB::statement("ALTER TABLE promotion_actions ADD CONSTRAINT promotion_rewards_type_check CHECK (type IN ('".ActionType::UpdateRebateEstimate->value."', '".ActionType::GiveFreeProduct->value."'))");
    }
};
