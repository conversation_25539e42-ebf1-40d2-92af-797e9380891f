<?php

declare(strict_types=1);

use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('promotions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuidMorphs('promotionable');
            $table->uuid('vendor_id');
            $table->string('name');
            $table->enum('type', [
                PromotionType::Rebate->value,
                PromotionType::BuyXGetY->value,
            ]);
            $table->text('description')->nullable();
            $table->integer('priority');
            $table->timestamp('started_at');
            $table->timestamp('ended_at')->nullable();
            $table->enum('status', [
                'draft',
                'active',
                'inactive',
            ]);
            $table->timestamps();

            $table->foreign('vendor_id')->references('id')->on('vendors');
        });

        Schema::create('promotion_product_offers', function (Blueprint $table) {
            $table->uuid('promotion_id');
            $table->uuid('product_offer_id');
            $table->timestamps();

            $table->foreign('promotion_id')->references('id')->on('promotions');
            $table->foreign('product_offer_id')->references('id')->on('product_offers');

            $table->unique(['promotion_id', 'product_offer_id']);
        });

        Schema::create('promotion_rules', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('promotion_id');
            $table->integer('priority');
            $table->timestamps();

            $table->foreign('promotion_id')->references('id')->on('promotions');
        });

        Schema::create('promotion_conditions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('promotion_rule_id');
            $table->enum('type', [
                ConditionType::MinimumSpendAmount->value,
                ConditionType::MinimumYearOverYearSpendGrowthPercent->value,
                ConditionType::MinimumQuantity->value,
            ]);
            $table->jsonb('config');
            $table->timestamps();

            $table->foreign('promotion_rule_id')->references('id')->on('promotion_rules');

            $table->unique(['promotion_rule_id', 'type']);
        });

        Schema::create('promotion_actions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('promotion_rule_id');
            $table->enum('type', [
                ActionType::UpdateRebateEstimate->value,
                ActionType::GiveFreeProduct->value,
            ]);
            $table->jsonb('config');
            $table->timestamps();

            $table->foreign('promotion_rule_id')->references('id')->on('promotion_rules');

            $table->unique(['promotion_rule_id', 'type']);
        });

        Schema::create('rebate_estimates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('promotion_id');
            $table->uuid('clinic_id');
            $table->integer('current_spend_amount');
            $table->decimal('current_rebate_percent', 4, 1);
            $table->integer('next_tier_minimum_spend_amount_threshold')->nullable();
            $table->decimal('next_tier_rebate_percent', 4, 1)->nullable();
            $table->integer('estimated_rebate_amount')->storedAs('current_spend_amount * (current_rebate_percent / 100)');
            $table->timestamps();

            $table->foreign('promotion_id')->references('id')->on('promotions');
            $table->foreign('clinic_id')->references('id')->on('clinics');

            $table->unique(['promotion_id', 'clinic_id']);
        });
    }
};
