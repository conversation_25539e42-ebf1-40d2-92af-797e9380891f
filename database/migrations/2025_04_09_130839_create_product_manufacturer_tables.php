<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_manufacturers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->timestamps();
        });

        Schema::create('product_manufacturer_mappings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_manufacturer_id');
            $table->string('name');
            $table->timestamps();

            $table->foreign('product_manufacturer_id')->references('id')->on('product_manufacturers');

            $table->unique(['product_manufacturer_id', 'name']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->uuid('product_manufacturer_id')->nullable();

            $table->foreign('product_manufacturer_id')->references('id')->on('product_manufacturers');
        });

        if (app()->environment('testing') === false) {
            DB::unprepared(file_get_contents(database_path('migrations/2025_04_09_130839_product_manufacturers.sql')));
        }
    }
};
