<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('catalog_sync_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(Vendor::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Clinic::class)->constrained()->cascadeOnDelete();
            $table->enum('status', CatalogSyncTaskStatus::values());
            $table->text('status_reason')->nullable();
            $table->timestamp('scheduled_at')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('catalog_sync_batches', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(CatalogSyncTask::class)->constrained()->cascadeOnDelete();
            $table->json('message');
            $table->enum('status', CatalogSyncBatchStatus::values());
            $table->text('status_reason')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('finished_at')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('primary_category', 'category');
            $table->dropColumn('secondary_category');
            $table->timestamp('deactivated_at')->nullable();
            $table->timestamp('last_synced_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('last_synced_at');
            $table->dropColumn('deactivated_at');
            $table->renameColumn('category', 'primary_category');
            $table->string('secondary_category')->nullable();
        });

        Schema::dropIfExists('catalog_sync_batches');

        Schema::dropIfExists('catalog_sync_tasks');
    }
};
