<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gpo_recommended_vendors', function (Blueprint $table) {
            $table->integer('order')->nullable();

            // TODO add to another migration after the existing data is corrected
            // $table->unique(['gpo_account_id', 'vendor_id'], 'unique_gpo_vendor');
            // $table->unique(['gpo_account_id', 'order'], 'unique_gpo_order');
        });
    }
};
