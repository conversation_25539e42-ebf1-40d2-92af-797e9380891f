<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('product_attributes', 'product_offer_attributes');
        Schema::rename('clinic_product', 'clinic_product_offer');
        Schema::rename('product_search_terms', 'product_offer_search_terms');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::rename('product_offer_attributes', 'product_attributes');
        Schema::rename('clinic_product_offer', 'clinic_product');
        Schema::rename('product_offer_search_terms', 'product_search_terms');
    }
};
