<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vendor_invoice_lines', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('vendor_invoice_id');
            $table->uuid('order_item_id')->nullable();
            $table->string('external_id')->nullable();
            $table->integer('quantity');
            $table->integer('unit_price');
            $table->integer('tax_fee');
            $table->integer('subtotal');
            $table->timestamps();

            $table->foreign('vendor_invoice_id')->references('id')->on('vendor_invoices');
            $table->foreign('order_item_id')->references('id')->on('order_items')->nullOnDelete();
            $table->unique(['vendor_invoice_id', 'order_item_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vendor_invoice_lines');
    }
};
