<?php

declare(strict_types=1);

use App\Models\OrderItem;
use App\Models\Shipment;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('order_item_shipment', function (Blueprint $table) {
            $table->foreignIdFor(OrderItem::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Shipment::class)->constrained()->cascadeOnDelete();

            $table->unique(['order_item_id', 'shipment_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('order_item_shipment');
    }
};
