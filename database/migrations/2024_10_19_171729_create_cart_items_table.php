<?php

declare(strict_types=1);

use App\Models\Cart;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignIdFor(Cart::class)
                ->constrained()
                ->cascadeOnDelete();

            $table->foreignUuid('product_id')->constrained()->cascadeOnDelete();

            $table->foreignIdFor(User::class)
                ->nullable()
                ->cascadeOnDelete();

            $table->unsignedInteger('price');
            $table->unsignedInteger('quantity');
            $table->unsignedInteger('subtotal')->storedAs('price * quantity');

            $table->string('notes')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
