<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_brands', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_manufacturer_id')->nullable();
            $table->string('name');
            $table->timestamps();

            $table->foreign('product_manufacturer_id')->references('id')->on('product_manufacturers');
        });

        Schema::create('product_brand_mappings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_brand_id');
            $table->string('name');
            $table->timestamps();

            $table->foreign('product_brand_id')->references('id')->on('product_brands');

            $table->unique(['product_brand_id', 'name']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->uuid('product_brand_id')->nullable();

            $table->foreign('product_brand_id')->references('id')->on('product_brands');
        });

        if (app()->environment('testing') === false) {
            DB::unprepared(file_get_contents(database_path('migrations/2025_04_09_145355_product_brands.sql')));
        }
    }
};
