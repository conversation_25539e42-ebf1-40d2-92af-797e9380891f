<?php

declare(strict_types=1);

use App\Enums\WebhookRequestStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('webhook_requests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('vendor_id')->constrained('vendors');
            $table->string('method');
            $table->string('slug')->nullable();
            $table->json('headers');
            $table->json('payload');
            $table->string('status')->default(WebhookRequestStatus::Pending->value);
            $table->text('error_message')->nullable();
            $table->timestamps();

            $table->index(['vendor_id', 'slug']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('webhook_requests');
    }
};
