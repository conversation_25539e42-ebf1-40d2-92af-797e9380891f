<?php

declare(strict_types=1);

use App\Modules\Order\Enums\ImportOrderItemCsvStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('import_order_item_csv_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('clinic_id');
            $table->string('file_path');
            $table->unsignedInteger('total_rows_count')->default(0);
            $table->unsignedInteger('processed_rows_count')->default(0);
            $table->string('status')->default(ImportOrderItemCsvStatus::Pending);
            $table->text('status_reason')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('finished_at')->nullable();
            $table->timestamps();

            $table->foreign('clinic_id')->references('id')->on('clinics');
        });
    }
};
