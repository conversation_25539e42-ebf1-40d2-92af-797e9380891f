<?php

declare(strict_types=1);

use App\Enums\ProductStockStatus;
use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('sku')->unique();
            $table->foreignIdFor(Vendor::class)->constrained()->cascadeOnDelete();
            $table->string('vendor_sku')->index();
            $table->text('description');
            $table->string('image_url');
            $table->unsignedInteger('price')->nullable();
            $table->unsignedInteger('increments')->default(1);
            $table->string('manufacturer')->nullable();
            $table->string('manufacturer_sku')->nullable();
            $table->enum('stock_status', ProductStockStatus::values())->default(ProductStockStatus::OutOfStock);
            $table->string('external_id')->nullable();
            $table->string('external_url')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->unique(['vendor_id', 'vendor_sku']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
