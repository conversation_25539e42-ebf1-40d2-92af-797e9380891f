<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $columnsToDrop = [
            'manufacturer',
            'manufacturer_sku',
            'is_hazardous',
            'requires_prescription',
            'requires_cold_shipping',
            'is_controlled_substance',
            'requires_pedigree',
            'sku',
            'image_url',
            'category',
            'description',
        ];

        foreach ($columnsToDrop as $column) {
            Schema::table('product_offers', function (Blueprint $table) use ($column) {
                $table->dropColumn($column);
            });
        }

        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('manufacturer');
        });

        Schema::dropIfExists('product_offer_search_terms');
        Schema::dropIfExists('product_offer_attributes');
    }
};
