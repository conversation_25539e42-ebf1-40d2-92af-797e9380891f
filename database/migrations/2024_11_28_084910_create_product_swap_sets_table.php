<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_swap_sets', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('sku')->unique();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::table('products', function (Blueprint $table) {
            $table->foreignUuid('product_swap_set_id')
                ->nullable()
                ->constrained()
                ->nullOnUpdate();

            $table->unique(['vendor_id', 'product_swap_set_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropUnique(['vendor_id', 'swap_set_id']);
            $table->dropForeign(['product_swap_set_id']);
            $table->dropColumn('product_swap_set_id');
        });

        Schema::dropIfExists('product_swap_sets');
    }
};
