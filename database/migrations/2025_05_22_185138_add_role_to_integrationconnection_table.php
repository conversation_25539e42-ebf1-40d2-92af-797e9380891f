<?php

declare(strict_types=1);

use App\Modules\Integration\Enums\IntegrationConnectionRole;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('integration_connections', function (Blueprint $table) {
            $table->enum('role', array_column(IntegrationConnectionRole::cases(), 'value'))->nullable();
        });
    }
};
