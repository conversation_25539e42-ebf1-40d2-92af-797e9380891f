<?php

declare(strict_types=1);

use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_account_receivable_contacts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(Vendor::class)->constrained()->cascadeOnDelete();
            $table->string('name')->nullable();
            $table->string('email');
            $table->softDeletes();
            $table->timestamps();
            $table->unique(['vendor_id', 'email']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_account_receivable_contacts');
    }
};
