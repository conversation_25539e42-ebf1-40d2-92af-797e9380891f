<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('integration_events', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_session_id');
            $table->enum('status', ['success', 'error']);
            $table->string('action');
            $table->nullableMorphs('subject');
            $table->jsonb('metadata')->nullable();
            $table->timestamps();

            $table->foreign('integration_session_id')->references('id')->on('integration_sessions');
        });
    }
};
