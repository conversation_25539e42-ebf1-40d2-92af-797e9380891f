<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('type')->index();
            $table->string('name')->nullable();
            $table->uuid('gpo_account_id')->nullable();
            $table->string('gpo_membership_number')->nullable();
            $table->timestamps();
        });

        Schema::table('accounts', function (Blueprint $table) {
            $table->foreign('gpo_account_id')->references('id')->on('accounts')->nullOnDelete();
        });

        Schema::create('clinic_account_details', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('clinic_account_id')->constrained('accounts')->cascadeOnDelete();
            $table->string('ein', 10)->nullable();
            $table->string('phone_number', 15)->nullable();
            $table->timestamps();
        });

        Schema::create('gpo_account_details', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('gpo_account_id')->constrained('accounts')->cascadeOnDelete();
            $table->string('image_path')->nullable();
            $table->timestamps();
        });
    }
};
