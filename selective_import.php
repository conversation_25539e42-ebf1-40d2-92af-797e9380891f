<?php

// Get top manufacturers with their product counts
$manufacturers = DB::select("
    SELECT pm.id, pm.name, COUNT(p.id) as product_count 
    FROM product_manufacturers pm 
    LEFT JOIN products p ON pm.id = p.product_manufacturer_id 
    WHERE p.deleted_at IS NULL 
    GROUP BY pm.id, pm.name 
    HAVING COUNT(p.id) > 0
    ORDER BY product_count DESC 
    LIMIT 15
");

echo "Found " . count($manufacturers) . " manufacturers\n";

$totalIndexed = 0;

foreach ($manufacturers as $manufacturer) {
    echo "Processing manufacturer: {$manufacturer->name} ({$manufacturer->product_count} products)\n";
    
    // Get up to 100 products for this manufacturer
    $products = \App\Models\Product::where('product_manufacturer_id', $manufacturer->id)
        ->whereNull('deleted_at')
        ->orderBy('created_at', 'desc')
        ->limit(100)
        ->get();
    
    echo "  Selected " . count($products) . " products for indexing\n";
    
    // Index these products in small batches
    $chunks = $products->chunk(10);
    
    foreach ($chunks as $chunk) {
        foreach ($chunk as $product) {
            try {
                $product->searchable();
                $totalIndexed++;
                echo "  Indexed: {$product->name}\n";
            } catch (Exception $e) {
                echo "  Error indexing product {$product->id}: " . $e->getMessage() . "\n";
            }
        }
        
        // Small delay to prevent overwhelming the system
        usleep(100000); // 0.1 second
    }
    
    echo "  Completed manufacturer: {$manufacturer->name}\n\n";
}

echo "Selective import completed! Total products indexed: {$totalIndexed}\n";
