/* Base */

body,
body *:not(html):not(style):not(br):not(tr):not(code) {
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    position: relative;
}

body {
    -webkit-text-size-adjust: none;
    background-color: #ffffff;
    color: #475569; /* Tailwind slate-600 for default text */
    height: 100%;
    line-height: 1.4;
    margin: 0;
    padding: 0;
    width: 100% !important;
}

p,
ul,
ol,
blockquote {
    line-height: 1.4;
    text-align: left;
}

a {
    color: #2563eb; /* Tailwind blue-600 */
}

a img {
    border: none;
}

/* Typography */

h1 {
    color: #1e293b; /* Tailwind slate-800 */
    font-size: 18px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

h2 {
    font-size: 16px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

h3 {
    font-size: 14px;
    font-weight: bold;
    margin-top: 0;
    text-align: left;
}

p {
    font-size: 16px;
    line-height: 1.5em;
    margin-top: 0;
    text-align: left;
}

p.sub {
    font-size: 12px;
}

img {
    max-width: 100%;
}

/* Layout */

.wrapper {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f8fafc; /* Tailwind slate-50 */
    margin: 0;
    padding: 0;
    width: 100%;
}

.content {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Header */

.header {
    padding: 25px 0;
    text-align: center;
}

.header a {
    color: #1e293b; /* Tailwind slate-800 */
    font-size: 19px;
    font-weight: bold;
    text-decoration: none;
}

/* Logo */

.logo {
    height: 48px;
    max-height: 48px;
    width: auto;
}

/* Body */

.body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    background-color: #f8fafc; /* Tailwind slate-50 */
    border-bottom: 1px solid #f8fafc;
    border-top: 1px solid #f8fafc;
    margin: 0;
    padding: 0;
    width: 100%;
}

.inner-body {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 640px;
    background-color: #ffffff;
    border-color: #e2e8f0; /* Tailwind slate-200 */
    border-radius: 2px;
    border-width: 1px;
    box-shadow: 0 2px 0 rgba(0, 0, 150, 0.025), 2px 4px 0 rgba(0, 0, 150, 0.015);
    margin: 0 auto;
    padding: 0;
    width: 640px;
}

/* Subcopy */

.subcopy {
    border-top: 1px solid #e2e8f0; /* Tailwind slate-200 */
    margin-top: 25px;
    padding-top: 25px;
}

.subcopy p {
    font-size: 14px;
}

/* Footer */

.footer {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 640px;
    margin: 0 auto;
    padding: 0;
    text-align: center;
    width: 640px;
}

.footer p {
    color: #94a3b8; /* Tailwind slate-400 */
    font-size: 12px;
    text-align: center;
}

.footer a {
    color: #94a3b8; /* Tailwind slate-400 */
    text-decoration: underline;
}

/* Tables */

.table table {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    width: 100%;
}

.table th {
    border-bottom: 1px solid #e2e8f0; /* Tailwind slate-200 */
    margin: 0;
    padding-bottom: 8px;
}

.table td {
    color: #64748b; /* Tailwind slate-500 */
    font-size: 15px;
    line-height: 18px;
    margin: 0;
    padding: 10px 0;
}

.content-cell {
    max-width: 100vw;
    padding: 32px;
}

/* Buttons */

.action {
    -premailer-cellpadding: 0;
    -premailer-cellspacing: 0;
    -premailer-width: 100%;
    margin: 30px auto;
    padding: 0;
    text-align: center;
    width: 100%;
    float: unset;
}

.button {
    -webkit-text-size-adjust: none;
    border-radius: 4px;
    color: #1e293b; /* Changed to Tailwind slate-800 for dark gray text */
    display: inline-block;
    overflow: hidden;
    text-decoration: none;
    font-weight: 600;
}

.button-blue,
.button-primary {
    color: #333333;
    background-color: #fadf82;
    border-bottom: 8px solid #fadf82;
    border-left: 18px solid #fadf82;
    border-right: 18px solid #fadf82;
    border-top: 8px solid #fadf82;
}

.button-green,
.button-success {
    background-color: #22c55e; /* Tailwind green-500 */
    border-bottom: 8px solid #22c55e;
    border-left: 18px solid #22c55e;
    border-right: 18px solid #22c55e;
    border-top: 8px solid #22c55e;
}

.button-red,
.button-error {
    background-color: #ef4444; /* Tailwind red-500 */
    border-bottom: 8px solid #ef4444;
    border-left: 18px solid #ef4444;
    border-right: 18px solid #ef4444;
    border-top: 8px solid #ef4444;
}

/* Panels */

.panel {
    border-left: #2563eb solid 4px; /* Tailwind blue-600 */
    margin: 21px 0;
}

.panel-content {
    background-color: #f8fafc; /* Tailwind slate-50 */
    color: #64748b; /* Tailwind slate-500 */
    padding: 16px;
}

.panel-content p {
    color: #64748b; /* Tailwind slate-500 */
}

.panel-item {
    padding: 0;
}

.panel-item p:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Utilities */

.break-all {
    word-break: break-all;
}
