{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "dedoc/scramble": "^0.12.10", "http-interop/http-factory-guzzle": "^1.2", "kirschbaum-development/eloquent-power-joins": "^4.0", "lab404/laravel-impersonate": "^1.7", "laravel/framework": "^12.0", "laravel/horizon": "^5.29", "laravel/nightwatch": "^1.7", "laravel/nova": "^5.0", "laravel/pennant": "^1.16", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/tinker": "^2.9", "laravel/vapor-core": "^2.37", "lcobucci/jwt": "^5.5", "league/csv": "^9.18", "league/flysystem-aws-s3-v3": "^3.0", "meilisearch/meilisearch-php": "^1.10", "sentry/sentry-laravel": "^4.10", "shyim/opensearch-php-dsl": "^1.1", "spatie/laravel-data": "^4.13", "spatie/laravel-json-api-paginate": "^1.16", "spatie/laravel-permission": "^6.16", "spatie/laravel-query-builder": "^6.3", "spatie/laravel-slack-alerts": "^1.5", "spatie/simple-excel": "^3.7", "stechstudio/laravel-zipstream": "^5.4", "tightenco/parental": "^1.4", "zing/laravel-scout-opensearch": "^3.4"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "laravel/breeze": "^2.2", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/telescope": "^5.2", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.4", "pestphp/pest-plugin-laravel": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan nova:publish"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"nova": {"type": "composer", "url": "https://nova.laravel.com"}}}