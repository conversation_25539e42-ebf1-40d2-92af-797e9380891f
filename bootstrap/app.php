<?php

declare(strict_types=1);

use App\Http\Middleware\ConvertRequestToSnakeCase;
use App\Http\Middleware\ConvertResponseToCamelCase;
use App\Http\Middleware\EnsureEmailIsVerified;
use App\Http\Middleware\EnsureHeaderIsPresent;
use App\Modules\Account\Providers\AccountServiceProvider;
use App\Modules\CatalogSync\Providers\CatalogSyncServiceProvider;
use App\Modules\Clinic\Providers\ClinicServiceProvider;
use App\Modules\Dashboard\DashboardServiceProvider;
use App\Modules\Gpo\Providers\GpoServiceProvider;
use App\Modules\Integration\Providers\IntegrationServiceProvider;
use App\Modules\Order\OrderServiceProvider;
use App\Modules\Product\Providers\ProductServiceProvider;
use App\Modules\Promotion\Providers\PromotionServiceProvider;
use App\Modules\SavedItems\Providers\SavedItemsServiceProvider;
use App\Modules\User\Providers\UserServiceProvider;
use App\Support\Jwt\JwtServiceProvider;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Lab404\Impersonate\ImpersonateServiceProvider;
use Laravel\Sanctum\Http\Middleware\CheckForAnyAbility;
use Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        AccountServiceProvider::class,
        ClinicServiceProvider::class,
        CatalogSyncServiceProvider::class,
        GpoServiceProvider::class,
        IntegrationServiceProvider::class,
        OrderServiceProvider::class,
        DashboardServiceProvider::class,
        ProductServiceProvider::class,
        PromotionServiceProvider::class,
        SavedItemsServiceProvider::class,
        JwtServiceProvider::class,
        UserServiceProvider::class,
        ImpersonateServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->statefulApi();

        $middleware->api(prepend: [
            ConvertRequestToSnakeCase::class,
            ConvertResponseToCamelCase::class,
            EnsureFrontendRequestsAreStateful::class,
        ]);

        $middleware->alias([
            'ability' => CheckForAnyAbility::class,
            'verified' => EnsureEmailIsVerified::class,
            'header' => EnsureHeaderIsPresent::class,
        ]);

        //
    })
    ->withEvents(discover: [
        __DIR__.'/../app/Modules/*/Listeners',
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        Integration::handles($exceptions);
    })->create();
