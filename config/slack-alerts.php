<?php

declare(strict_types=1);

return [
    /*
     * The webhook URLs that we'll use to send a message to Slack.
     */
    'webhook_urls' => [
        'default' => env('SLACK_ALERT_WEBHOOK'),
        'orders_failures' => env('SLACK_ALERT_ORDERS_FAILURES_WEBHOOK'),
        'orders_log' => env('SLACK_ALERT_ORDERS_LOG_WEBHOOK'),
    ],

    /*
     * This job will send the message to Slack. You can extend this
     * job to set timeouts, retries, etc...
     */
    'job' => <PERSON><PERSON>\SlackAlerts\Jobs\SendToSlackChannelJob::class,
    'queue' => env('SLACK_ALERT_QUEUE', 'default'),
];
