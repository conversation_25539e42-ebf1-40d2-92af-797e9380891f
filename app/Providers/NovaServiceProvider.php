<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\User as UserModel;
use App\Modules\Account\Nova\ClinicAccount;
use App\Modules\Account\Nova\User;
use App\Modules\Gpo\Nova\GpoAccount;
use App\Modules\Gpo\Nova\GpoUser;
use App\Modules\Integration\Nova\IntegrationConnection;
use App\Modules\Order\Nova\ImportOrderHistoryTask;
use App\Modules\Order\Nova\ImportOrderItemCsvTask;
use App\Modules\Product\Nova\ProductBrand;
use App\Modules\Product\Nova\ProductManufacturer;
use App\Modules\Promotion\Nova\Promotion;
use App\Nova\CatalogSyncBatch;
use App\Nova\CatalogSyncTask;
use App\Nova\Clinic;
use App\Nova\Dashboards\Main;
use App\Nova\Order;
use App\Nova\OrderPromotion;
use App\Nova\Product;
use App\Nova\SubOrder;
use App\Nova\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\Menu\MenuItem;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;
use Parental\Providers\NovaResourceProvider;

final class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Nova::withBreadcrumbs();

        Nova::mainMenu(fn (Request $request) => [
            MenuSection::dashboard(Main::class)->icon('chart-square-bar'),

            MenuSection::make('GPO', [
                MenuItem::resource(GpoAccount::class)->name('Accounts'),
                MenuItem::resource(GpoUser::class)->name('Users'),
            ])->icon('building-office'),

            MenuSection::make('Clinic', [
                MenuItem::resource(ClinicAccount::class)->name('Accounts'),
                MenuItem::resource(User::class)->name('Users'),
            ])->icon('user-group'),

            MenuSection::make('Clinics', [
                MenuItem::resource(Clinic::class),
                MenuItem::resource(ImportOrderHistoryTask::class),
                MenuItem::resource(ImportOrderItemCsvTask::class),
            ])->icon('office-building'),

            MenuSection::make('Accounting', [
                MenuItem::resource(Order::class),
                MenuItem::resource(SubOrder::class),
                MenuItem::resource(OrderPromotion::class),
                // MenuItem::resource(Invoice::class),
            ])->icon('currency-dollar'),

            MenuSection::make('Vendors', [
                MenuItem::resource(Vendor::class),
                MenuItem::resource(Product::class),
                MenuItem::resource(ProductBrand::class),
                MenuItem::resource(ProductManufacturer::class),
                MenuItem::resource(IntegrationConnection::class),
            ])->icon('shopping-cart'),

            MenuSection::make('Promotions', [
                MenuItem::resource(Promotion::class),
            ])->icon('beaker'),

            MenuSection::make('Catalog Sync', [
                MenuItem::resource(CatalogSyncTask::class),
                MenuItem::resource(CatalogSyncBatch::class),
            ])->icon('arrow-path'),
        ]);

        $this->app->register(NovaResourceProvider::class);
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        parent::register();
        //
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
            ->withAuthenticationRoutes(default: true)
            ->withPasswordResetRoutes()
            ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function (UserModel $user) {
            return in_array($user->email, [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ]);
        });
    }

    /**
     * Get the dashboards that should be listed in the Nova sidebar.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [
            Main::make()->showRefreshButton(),
        ];
    }
}
