<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\User;
use Aws\Kms\KmsClient;
use Aws\Sqs\SqsClient;
use Illuminate\Auth\Middleware\RedirectIfAuthenticated;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use Lab404\Impersonate\Events\LeaveImpersonation;
use Lab404\Impersonate\Events\TakeImpersonation;

final class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        RedirectIfAuthenticated::redirectUsing(fn () => '/api/users/me');

        ResetPassword::createUrlUsing(function (object $notifiable, string $token) {
            return config('app.frontend_url')."/password-reset/$token?email={$notifiable->getEmailForPasswordReset()}";
        });

        JsonResource::withoutWrapping();

        Gate::define('viewApiDocs', function (User $user) {
            return in_array($user->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);
        });

        Gate::define('viewPulse', function (User $user) {
            return in_array($user->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);
        });

        Schema::morphUsingUuids();

        // Bindings
        $binds = [
            KmsClient::class => fn () => new KmsClient([
                'version' => '2014-11-01',
            ]),
            SqsClient::class => fn () => new SqsClient([
                'version' => '2012-11-05',
            ]),
        ];

        foreach ($binds as $abstract => $concrete) {
            $this->app->bind($abstract, $concrete);
        }

        // https://github.com/404labfr/laravel-impersonate/issues/219#issuecomment-2689990251
        Event::listen(function (TakeImpersonation $event) {
            session()->put([
                'password_hash_web' => $event->impersonated->getAuthPassword(),
            ]);
        });

        Event::listen(function (LeaveImpersonation $event) {
            session()->remove('password_hash_web');
            session()->put([
                'password_hash_web' => $event->impersonator->getAuthPassword(),
            ]);
            Auth::setUser($event->impersonator);
        });
    }
}
