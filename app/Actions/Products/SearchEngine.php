<?php

declare(strict_types=1);

namespace App\Actions\Products;

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log as FacadesLog;
use OpenSearch\Client;
use OpenSearchDSL\Query\Compound\BoolQuery;
use OpenSearchDSL\Query\Compound\FunctionScoreQuery;
use OpenSearchDSL\Query\FullText\MultiMatchQuery;
use OpenSearchDSL\Query\Span\SpanFirstQuery;
use OpenSearchDSL\Query\Span\SpanTermQuery;
use OpenSearchDSL\Query\TermLevel\TermQuery;
use OpenSearchDSL\Query\TermLevel\TermsQuery;
use OpenSearchDSL\Search;
use Throwable;

final class SearchEngine
{
    private const RECENT_ORDERS_DAYS = 30;

    public function __construct(
        private readonly Client $client
    ) {}

    public function handle(Clinic $clinic, string $searchTerm, array $filter = []): ?array
    {
        $vendorIds = $this->getConnectedVendorIds($clinic);

        if (isset($filter['vendor_ids'])) {
            $vendorIds = array_values(array_intersect($vendorIds, explode(',', $filter['vendor_ids'])));
        }

        $mainQuery = $this->buildMainQuery($searchTerm, $vendorIds);
        $functionScoreQuery = $this->buildScoringQuery($mainQuery, $clinic);

        $search = new Search();
        $search->addQuery($functionScoreQuery);
        $search->setSize(1000);

        $queryArray = $search->toArray();

        try {
            $response = $this->client->search([
                'index' => (new Product())->searchableAs(),
                'body' => [
                    ...$queryArray,
                    '_source' => false,
                ],
            ]);

            $productIds = collect($response['hits']['hits'] ?? [])
                ->map(fn ($hit) => $hit['_id'])
                ->all();
            $products = $this->fetchProducts($productIds, $clinic, $vendorIds);

            return [$products, $productIds];
        } catch (Throwable $e) {
            FacadesLog::error('Search query failed', [
                'query' => $queryArray,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    private function getConnectedVendorIds(Clinic $clinic): array
    {
        return IntegrationConnection::query()
            ->whereIn('status', [IntegrationConnectionStatus::Connecting, IntegrationConnectionStatus::Connected])
            ->where('clinic_id', $clinic->id)
            ->pluck('vendor_id')
            ->unique()
            ->values()
            ->toArray();
    }

    private function buildMainQuery(string $searchTerm, array $connectedVendorIds): BoolQuery
    {
        $boolQuery = new BoolQuery();

        $vendorSkusQuery = new TermQuery('vendorSkus', $searchTerm, ['boost' => 5.0]);
        $vendorSkusAlphaNumQuery = new TermQuery('vendorSkusAlphaNum', mb_strtolower($searchTerm), ['boost' => 5.0]);
        $manufacturerSkuQuery = new TermQuery('manufacturerSku', $searchTerm, ['boost' => 5.0]);

        $boolQuery->add($vendorSkusQuery, BoolQuery::SHOULD);
        $boolQuery->add($vendorSkusAlphaNumQuery, BoolQuery::SHOULD);
        $boolQuery->add($manufacturerSkuQuery, BoolQuery::SHOULD);

        $multiMatchBestFieldsQuery = new MultiMatchQuery(
            ['name', 'searchTerms'],
            $searchTerm,
            [
                'fuzziness' => 1,
                'type' => 'best_fields',
                'operator' => 'and',
            ],
        );
        $boolQuery->add($multiMatchBestFieldsQuery, BoolQuery::SHOULD);

        $multiMatchPhrasePrefixQuery = new MultiMatchQuery(
            ['name', 'searchTerms'],
            $searchTerm,
            [
                'type' => 'phrase_prefix',
            ],
        );
        $boolQuery->add($multiMatchPhrasePrefixQuery, BoolQuery::SHOULD);

        $spanFirstQuery = new SpanFirstQuery(
            new SpanTermQuery('name', mb_strtolower($searchTerm)),
            1,
            ['boost' => 4]
        );
        $boolQuery->add($spanFirstQuery, BoolQuery::SHOULD);

        $boolQuery->addParameter('minimum_should_match', '1');

        $mainQuery = new BoolQuery;
        $mainQuery->add($boolQuery, BoolQuery::MUST);

        $filterQuery = new TermsQuery('vendorIds', $connectedVendorIds);
        $mainQuery->add($filterQuery, BoolQuery::FILTER);

        return $mainQuery;
    }

    private function buildScoringQuery(BoolQuery $mainQuery, Clinic $clinic): FunctionScoreQuery
    {
        $functionScoreQuery = new FunctionScoreQuery($mainQuery, [
            'score_mode' => 'max',
            'boost_mode' => 'multiply',
        ]);

        $this->addCoumpoundingPharmacyWeight($functionScoreQuery, $clinic);
        $this->addGpoRecommendedBoostWeight($functionScoreQuery, $clinic);
        $this->addFavoriteProductsBoostWeight($functionScoreQuery, $clinic);
        $this->addRecentlyOrderedBoostWeight($functionScoreQuery, $clinic);

        return $functionScoreQuery;
    }

    private function fetchProducts(array $productIds, Clinic $clinic, array $filterVendorIds): Builder
    {
        $vendorIds = $clinic->vendors->pluck('id')->unique()->values()->toArray();

        return Product::query()
            ->whereHas('productOffers', function ($query) use ($clinic, $filterVendorIds) {
                $query->whereVendorIdIn($filterVendorIds)
                    ->withVendorConnected([$clinic->id])
                    ->withPriceForFrontend($clinic)
                    ->withActiveOffers()
                    ->where(function ($priceQuery) use ($clinic) {
                        $priceQuery->where(function ($subQuery) use ($clinic) {
                            $subQuery->whereHas('clinics', function ($clinicQuery) use ($clinic) {
                                $clinicQuery->where('clinic_id', $clinic->id)
                                    ->whereNotNull('clinic_product_offer.price');
                            });
                        })->orWhereNotNull('price');
                    });
            })
            ->with(['productOffers' => function ($query) use ($clinic, $vendorIds) {
                $query->whereVendorIdIn($vendorIds)
                    ->withClinic($clinic->id)
                    ->withActiveOffers()
                    ->withLastClinicOrderItem($clinic->id)
                    ->withClinicCartItem($clinic->id)
                    ->withVendorConnected([$clinic->id])
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                    ->withPriceForFrontend($clinic);
            }])
            ->whereIn('id', $productIds);

    }

    private function addCoumpoundingPharmacyWeight(FunctionScoreQuery $functionScoreQuery, Clinic $clinic): void
    {
        $wedgewood = Vendor::query()->where('slug', 'wedgewood')->first();

        if ($wedgewood) {
            $functionScoreQuery->addWeightFunction(
                0.75,
                new TermsQuery('vendorIds', [$wedgewood->id]),
            );
        }
    }

    private function addGpoRecommendedBoostWeight(FunctionScoreQuery $functionScoreQuery, Clinic $clinic): void
    {
        $gpoId = $clinic->account?->gpo?->id;

        if (! $gpoId) {
            return;
        }

        $productIds = ProductOffer::whereHas('gpos', function (Builder $query) use ($gpoId) {
            $query->where('gpo_recommended_products.gpo_account_id', $gpoId);
        })->pluck('product_id')->unique()->values()->toArray();

        if (count($productIds) > 0) {
            $functionScoreQuery->addWeightFunction(
                10.0,
                new TermsQuery('id', $productIds),
            );
        }
    }

    private function addFavoriteProductsBoostWeight(FunctionScoreQuery $functionScoreQuery, Clinic $clinic): void
    {
        $productIds = $clinic->favoriteProducts()->pluck('clinic_product_favorites.product_id')->unique()->values()->toArray();
        if (! $productIds) {
            return;
        }

        $functionScoreQuery->addWeightFunction(
            5.0,
            new TermsQuery('id', $productIds),
        );
    }

    private function addRecentlyOrderedBoostWeight(FunctionScoreQuery $functionScoreQuery, Clinic $clinic): void
    {
        $productIds = ProductOffer::whereHas('orderItems', function (Builder $query) use ($clinic) {
            $query->whereHas('order', function (Builder $orders) use ($clinic) {
                $orders->where('clinic_id', $clinic->id)
                    ->where('created_at', '>=', now()->subDays(self::RECENT_ORDERS_DAYS));
            });
        })->pluck('product_id')->unique()->values()->toArray();

        if (! $productIds) {
            return;
        }

        $functionScoreQuery->addWeightFunction(
            3.0,
            new TermsQuery('id', $productIds),
        );

    }
}
