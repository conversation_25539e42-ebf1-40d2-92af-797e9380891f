<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\ClinicBudgetSettings;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

final class ClinicBudgetSettingsPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ClinicBudgetSettings $settings): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($settings->clinic->account_id);
    }

    /**
     * Determine whether the user can create the model.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ClinicBudgetSettings $settings): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($settings->clinic->account_id);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ClinicBudgetSettings $settings): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($settings->clinic->account_id);
    }
}
