<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use Illuminate\Auth\Access\HandlesAuthorization;

final class ClinicPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Clinic $clinic): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($clinic->clinic_account_id);
    }

    public function create(User $user): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->hasRole([ClinicAccountRole::Owner, ClinicAccountRole::Admin]);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Clinic $clinic): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($clinic->clinic_account_id);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Clinic $clinic): bool
    {
        if ($user->can('viewNova')) {
            return true;
        }

        return $user->belongsToAccount($clinic->clinic_account_id);
    }

    /**
     * Determine whether the user can attach any user to the clinic.
     */
    public function attachUser(User $user, Clinic $clinic, User $userToAttach): bool
    {
        return $userToAttach->belongsToAccount($clinic->clinic_account_id);
    }

    /**
     * Determine whether the user can attach any vendor to the clinic.
     */
    public function attachAnyVendor(User $user, Clinic $clinic): bool
    {
        return false;
    }

    /**
     * Determine whether the user can detach a vendor from the clinic.
     */
    public function detachVendor(User $user, Clinic $clinic, Vendor $vendor): bool
    {
        return false;
    }
}
