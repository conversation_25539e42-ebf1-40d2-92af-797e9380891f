<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;
use App\Modules\Gpo\Models\GpoUser;

final class GpoUserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, GpoUser $gpoUser): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, GpoUser $gpoUser): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, GpoUser $gpoUser): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, GpoUser $gpoUser): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, GpoUser $gpoUser): bool
    {
        return $user->can('viewNova');
    }

    /**
     * Determine whether the user can upload files.
     */
    public function uploadFiles(User $user): bool
    {
        return $user->can('viewNova');
    }
}
