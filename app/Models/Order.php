<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\AddressType;
use App\Enums\OrderItemStatus;
use App\Enums\PaymentMethod;
use App\Observers\OrderObserver;
use App\QueryBuilders\OrderQueryBuilder;
use App\Traits\Statusable;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\URL;

#[ObservedBy([OrderObserver::class])]
final class Order extends Model
{
    use HasFactory, HasUuids, SoftDeletes, Statusable;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payment_method' => PaymentMethod::class,
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'total_price',
        'status',
    ];

    /**
     * Create a new order from the cart.
     */
    public static function createFromCart(User $user, Cart $cart, array $data): self
    {
        $order = self::create([
            'clinic_id' => $cart->clinic_id,
            'user_id' => $user->id,
            'payment_method' => $data['payment_method'],
        ]);

        if ($data['is_billing_same_as_shipping_address'] ?? false) {
            $data['shipping_address'] = $data['billing_address'];
        }

        $order->createVendorSubOrders($cart->items);
        $order->addItems($cart->items);
        $order->addBillingAddress($data['billing_address']);
        $order->addShippingAddress($data['shipping_address']);

        return $order;
    }

    /**
     * Create a new Eloquent query builder for the model.
     */
    public function newEloquentBuilder($query): OrderQueryBuilder
    {
        return new OrderQueryBuilder($query);
    }

    public function scopeByClinic(Builder $query, string $clinicId): Builder
    {
        return $query->where('clinic_id', $clinicId);
    }

    public function scopeDateFrom(Builder $query, string $value): Builder
    {
        return $query->whereDate('created_at', '>=', $value);
    }

    public function scopeDateTo(Builder $query, string $value): Builder
    {
        return $query->whereDate('created_at', '<=', $value);
    }

    /**
     * Get the clinic that owns the order.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the user that owns the order.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order items for the order.
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * The billing address that belongs to the order.
     */
    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable')->where('type', AddressType::Billing);
    }

    /**
     * The shipping address that belongs to the order.
     */
    public function shippingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable')->where('type', AddressType::Shipping);
    }

    /**
     * Get the suborders for the order.
     */
    public function suborders(): HasMany
    {
        return $this->hasMany(SubOrder::class);
    }

    public function shipments(): HasManyThrough
    {
        return $this->hasManyThrough(Shipment::class, SubOrder::class);
    }

    /**
     * Get the promotions applied to this order.
     */
    public function promotions(): HasMany
    {
        return $this->hasMany(OrderPromotion::class);
    }

    /**
     * The total price of the order.
     */
    protected function totalPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->items->sum(fn (OrderItem $item) => $item->total_price),
        );
    }

    /**
     * The status of the order.
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->determineOrderStatus()
        );
    }

    protected function downloadInvoicesUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->createDownloadInvoicesUrl()
        );
    }

    protected function downloadChecklistUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => URL::signedRoute('order.checklist.download', ['order' => $this->id])
        );
    }

    /**
     * Determine the status of the order based on the items statuses.
     */
    private function determineOrderStatus(): OrderItemStatus
    {
        $hasError = $this->suborders()
            ->hasError()
            ->exists();

        $statusCounts = $this->items()
            ->selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return $this->generateStatus($statusCounts, $hasError);
    }

    private function createDownloadInvoicesUrl(): ?string
    {
        return $this->suborders->pluck('externalOrders')->collapse()
            ->whereNotNull('invoice_file_path')
            ->isEmpty() ? null
            : URL::signedRoute('order.invoice.download', ['order' => $this->id]);
    }

    /**
     * Create suborders for each vendor.
     */
    private function createVendorSubOrders(Collection $items): void
    {
        $items
            ->groupBy(fn ($item) => $item->vendor->id)
            ->each(fn ($items, $vendorId) => $this->suborders()->create(['vendor_id' => $vendorId]));
    }

    /**
     * Add items to the order.
     */
    private function addItems(Collection $items): void
    {
        $this->items()->createMany($items->map(fn ($item) => [
            'product_offer_id' => $item->product_offer_id,
            'quantity' => $item->quantity,
            'price' => $item->price,
            'status' => OrderItemStatus::Pending,
        ]));
    }

    /**
     * Add billing address to the order.
     */
    private function addBillingAddress(array $billingAddress): void
    {
        $this->billingAddress()->create([
            'type' => AddressType::Billing,
            'street' => $billingAddress['street'],
            'city' => $billingAddress['city'],
            'state' => $billingAddress['state'],
            'postal_code' => $billingAddress['postal_code'],
        ]);
    }

    /**
     * Add shipping address to the order.
     */
    private function addShippingAddress(array $shippingAddress): void
    {
        $this->shippingAddress()->create([
            'type' => AddressType::Shipping,
            'street' => $shippingAddress['street'],
            'city' => $shippingAddress['city'],
            'state' => $shippingAddress['state'],
            'postal_code' => $shippingAddress['postal_code'],
        ]);
    }
}
