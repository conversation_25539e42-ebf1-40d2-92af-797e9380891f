<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\SubOrder;
use <PERSON><PERSON>\SlackAlerts\Facades\SlackAlert;

final class SlackNotificationService
{
    /**
     * Send a Slack notification for a failed order placement.
     */
    public static function sendOrderPlacementFailed(SubOrder $suborder): void
    {
        if (! app()->isProduction()) {
            return;
        }

        $order = $suborder->order;
        $vendor = $suborder->vendor;
        $clinic = $order->clinic;
        $url = route('nova.pages.detail', ['resource' => 'sub-orders', 'resourceId' => $suborder->id]);

        $blocks = [
            [
                'type' => 'header',
                'text' => [
                    'type' => 'plain_text',
                    'text' => ':x: Order Placement Failed',
                    'emoji' => true,
                ],
            ],
            [
                'type' => 'section',
                'fields' => [
                    [
                        'type' => 'mrkdwn',
                        'text' => '*PO Number*',
                    ],
                    [
                        'type' => 'plain_text',
                        'text' => "#{$order->order_number}",
                    ],
                    [
                        'type' => 'mrkdwn',
                        'text' => '*Vendor*',
                    ],
                    [
                        'type' => 'plain_text',
                        'text' => $vendor->name,
                    ],
                    [
                        'type' => 'mrkdwn',
                        'text' => '*Clinic*',
                    ],
                    [
                        'type' => 'plain_text',
                        'text' => $clinic->name,
                    ],
                    [
                        'type' => 'mrkdwn',
                        'text' => '*Total Order Lines*',
                    ],
                    [
                        'type' => 'plain_text',
                        'text' => (string) $suborder->items()->count(),
                    ],
                ],
            ],
            [
                'type' => 'actions',
                'elements' => [
                    [
                        'type' => 'button',
                        'text' => [
                            'type' => 'plain_text',
                            'text' => 'View in Nova',
                        ],
                        'url' => $url,
                    ],
                ],
            ],
        ];

        SlackAlert::to('orders_failures')->blocks($blocks);
    }
}
