<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use App\Nova\Actions\RetryCatalogSyncBatch;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class CatalogSyncBatch extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Modules\CatalogSync\Models\CatalogSyncBatch>
     */
    public static $model = \App\Modules\CatalogSync\Models\CatalogSyncBatch::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Task', 'task', CatalogSyncTask::class),

            Select::make('Status', 'status')
                ->options(CatalogSyncBatchStatus::options())
                ->displayUsingLabels(),

            Text::make('Status Reason', 'status_reason'),

            Code::make('Message', 'message')
                ->json(),

            DateTime::make('Started At', 'started_at'),

            DateTime::make('Finished At', 'finished_at'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            new RetryCatalogSyncBatch(),
        ];
    }
}
