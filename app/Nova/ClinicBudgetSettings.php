<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\ClinicBudgetType;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Panel;

final class ClinicBudgetSettings extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ClinicBudgetSettings>
     */
    public static $model = \App\Models\ClinicBudgetSettings::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * Disable the ability to create new resources.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            BelongsTo::make('Clinic', 'clinic', Clinic::class)
                ->readonly()
                ->sortable(),

            Select::make('Type', 'type')
                ->options(ClinicBudgetType::options())
                ->required()
                ->displayUsingLabels()
                ->sortable(),

            Panel::make('External Data', $this->externalDataFields()),

            Panel::make('Static Budget', $this->staticBudgetFields()),

            Panel::make('Dynamic Budget', $this->dynamicBudgetFields()),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the fields for the external data panel.
     */
    private function externalDataFields(): array
    {
        return [
            Boolean::make('Include External Data', 'include_external_data'),

            Currency::make('External Weekly COGS', 'external_weekly_cogs')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),

            Currency::make('External Monthly COGS', 'external_monthly_cogs')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),
        ];
    }

    /**
     * Get the fields for the static budget panel.
     */
    private function staticBudgetFields(): array
    {
        return [
            Currency::make('Weekly COGS', 'weekly_cogs')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),

            Currency::make('Weekly GA', 'weekly_ga')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),

            Currency::make('Monthly COGS', 'monthly_cogs')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),

            Currency::make('Monthly GA', 'monthly_ga')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),
        ];
    }

    /**
     * Get the fields for the dynamic budget panel.
     */
    private function dynamicBudgetFields(): array
    {
        return [
            Number::make('Target COGS Percent', 'target_cogs_percent')
                ->help('Enter as decimal (e.g. 0.25 for 25%)')
                ->min(0)
                ->max(1)
                ->step(0.01)
                ->default(0),

            Number::make('Target GA Percent', 'target_ga_percent')
                ->help('Enter as decimal (e.g. 0.15 for 15%)')
                ->min(0)
                ->max(1)
                ->step(0.01)
                ->default(0),

            Currency::make('Avg Two Weeks Sales', 'avg_two_weeks_sales')
                ->help('Weekly average sales for the last two weeks')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),

            Currency::make('Month to Date Sales', 'month_to_date_sales')
                ->help('Total sales for the current month to date')
                ->currency('USD')
                ->asMinorUnits()
                ->min(0)
                ->step(0.01)
                ->default(0),
        ];
    }
}
