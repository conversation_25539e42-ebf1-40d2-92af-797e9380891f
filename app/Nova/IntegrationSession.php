<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationSession as IntegrationSessionModel;
use App\Modules\Integration\Nova\IntegrationConnection;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Badge;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Resource;

final class IntegrationSession extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<IntegrationSessionModel>
     */
    public static $model = IntegrationSessionModel::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'integration_point';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'status', 'connection_id', 'integration_point',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),
            Badge::make('Status')->map(IntegrationSessionStatus::colors()),
            Text::make('Integration Point', 'integration_point'),
            DateTime::make('Finished At'),
            BelongsTo::make('Connection', 'connection', IntegrationConnection::class),
            DateTime::make('Created At'),
            HasMany::make('Integration Events', 'events', IntegrationEvent::class),
        ];
    }
}
