<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\OrderItemStatus;
use App\Nova\Actions\PlaceVendorSubOrder;
use App\Nova\Actions\SyncVendorSubOrder;
use App\Nova\Filters\WithoutInvoiceAttachedFilter;
use App\Traits\Statusable;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\MorphMany;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class SubOrder extends Resource
{
    use Statusable;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\SubOrder>
     */
    public static $model = \App\Models\SubOrder::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'order.order_number';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'order.order_number',
        'external_id',
        'vendor.name',
    ];

    /**
     * Determine if the current user can create new resources.
     */
    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Date::make('Order Date', 'created_at')
                ->sortable(),

            BelongsTo::make('Order', 'order', Order::class)
                ->readonly()
                ->sortable(),

            BelongsTo::make('Vendor', 'vendor', Vendor::class)
                ->readonly()
                ->sortable(),

            Badge::make('Status', 'status')
                ->map($this->generateOptionMapping())
                ->labels(OrderItemStatus::options())
                ->sortable(),

            Currency::make('Total Price', 'total_price')
                ->onlyOnIndex()
                ->currency('USD')
                ->asMinorUnits()
                ->sortable(),

            Text::make('External ID', 'external_id')
                ->onlyOnDetail()
                ->sortable(),

            Text::make('External Status', 'external_status')
                ->onlyOnDetail()
                ->sortable(),

            Text::make('Error Code', 'error_code')
                ->onlyOnDetail()
                ->sortable(),

            Textarea::make('Error Message', 'error_message')
                ->onlyOnDetail()
                ->sortable(),

            HasMany::make('Items', 'items', OrderItem::class),

            HasMany::make('Shipments', 'shipments', Shipment::class),

            HasMany::make('External Orders', 'externalOrders', ExternalOrder::class),

            MorphMany::make('Integration Events', 'integrationEvents', IntegrationEvent::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new WithoutInvoiceAttachedFilter,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            new PlaceVendorSubOrder(),
            new SyncVendorSubOrder(),
        ];
    }
}
