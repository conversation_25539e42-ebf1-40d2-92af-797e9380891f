<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Models\IntegrationEvent as IntegrationEventModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Badge;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Resource;

final class IntegrationEvent extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<IntegrationEventModel>
     */
    public static $model = IntegrationEventModel::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'action';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'status', 'action', 'subject_type', 'subject_id',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable()->hideFromIndex(),
            Text::make('Action'),
            Badge::make('Status')
                ->map(IntegrationEventStatus::colors()),
            Code::make('Metadata')->json(),
            BelongsTo::make('Session', 'session', IntegrationSession::class),
            DateTime::make('Created At'),
        ];
    }
}
