<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\Order\Services\Vendor\Contracts\InvoiceSynchronizer;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Card;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Field;
use <PERSON>vel\Nova\Fields\File;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Lenses\Lens;

final class ExternalOrder extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Modules\Order\Models\ExternalOrder>
     */
    public static $model = \App\Modules\Order\Models\ExternalOrder::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'external_order_id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'external_order_id', 'status',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('External Order ID', 'external_order_id'),
            Text::make('Status', 'status'),
            File::make('Invoice', 'invoice_file_path')
                ->displayUsing(fn ($value) => $value ? 'Download' : 'N/A')
                ->disk(InvoiceSynchronizer::INVOICE_STORAGE_DISK),

            BelongsTo::make('Sub Order', 'subOrder', SubOrder::class),
        ];
    }

    /**
     * Get the cards available for the resource.
     *
     * @return array<int, Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
