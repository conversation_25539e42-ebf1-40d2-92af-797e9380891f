<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\AddressType;
use App\Modules\Account\Nova\ClinicAccount;
use <PERSON><PERSON>\Nova\Fields\MorphTo;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class Address extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Address>
     */
    public static $model = \App\Models\Address::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'street';

    /**
     * Disable the ability to create new resources.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'street', 'city', 'state', 'postal_code', 'country',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            MorphTo::make('Addressable')
                ->readonly()
                ->types([
                    ClinicAccount::class,
                    Clinic::class,
                    Order::class,
                ]),

            Select::make('Type', 'type')
                ->onlyOnForms()
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest())
                ->required()
                ->options(AddressType::options())
                ->displayUsingLabels()
                ->default(AddressType::Shipping),

            Text::make('Street Address', 'street')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('City', 'city')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('State', 'state')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Postal Code', 'postal_code')
                ->sortable()
                ->rules('required', 'max:255'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
