<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\Integration\Nova\IntegrationConnection;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class CatalogSyncTask extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Modules\CatalogSync\Models\CatalogSyncTask>
     */
    public static $model = \App\Modules\CatalogSync\Models\CatalogSyncTask::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Connection', 'connection', IntegrationConnection::class)
                ->searchable(),

            BelongsTo::make('Vendor', 'vendor', Vendor::class)
                ->readonly(),

            BelongsTo::make('Clinic', 'clinic', Clinic::class)
                ->readonly(),

            Text::make('Status Reason', 'status_reason'),

            Select::make('Status', 'status')
                ->required()
                ->options(CatalogSyncTaskStatus::class)
                ->displayUsingLabels(),

            DateTime::make('Scheduled At', 'scheduled_at'),

            HasMany::make('Batches', 'batches', CatalogSyncBatch::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
