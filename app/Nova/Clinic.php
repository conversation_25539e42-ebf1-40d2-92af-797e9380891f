<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\AccountRole;
use App\Modules\Account\Nova\ClinicAccount;
use App\Modules\Account\Nova\User;
use App\Nova\Actions\ImpersonateUser;
use App\Nova\Actions\LeaveImpersonation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphOne;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class Clinic extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Clinic>
     */
    public static $model = \App\Models\Clinic::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name', 'business_tax_id',
    ];

    /**
     * Filter out administrators from the users list.
     */
    public static function relatableUsers(NovaRequest $request, Builder $query)
    {
        return $query->whereNot('role', AccountRole::Administrator);
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make('ID', 'id')
                ->hideFromIndex()
                ->sortable(),

            Text::make('Name', 'name')
                ->sortable()
                ->rules('required', 'max:255'),

            BelongsTo::make('Account', 'account', ClinicAccount::class)
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest())
                ->searchable(),

            Text::make('Business Tax ID', 'business_tax_id')
                ->sortable()
                ->rules('required'),

            Text::make('Phone Number', 'phone_number')
                ->sortable()
                ->rules('required', 'max:255'),

            MorphOne::make('Shipping Address', 'shippingAddress', Address::class),

            MorphOne::make('Billing Address', 'billingAddress', Address::class),

            HasOne::make('Budget Settings', 'budgetSettings', ClinicBudgetSettings::class),

            BelongsToMany::make('Users', 'users', User::class),

            BelongsToMany::make('Products', 'productOffers', ProductOffer::class)
                ->searchable()
                ->fields(fn () => [
                    Currency::make('Price', 'price')
                        ->currency('USD')
                        ->asMinorUnits()
                        ->sortable()
                        ->required()
                        ->min(0)
                        ->step(0.01),
                ]),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            resolve(ImpersonateUser::class)
                ->sole()
                ->showInline(),
            resolve(LeaveImpersonation::class)
                ->onlyOnIndex()
                ->standalone()
                ->canSee(fn () => Auth::user()->isImpersonated()),
        ];
    }
}
