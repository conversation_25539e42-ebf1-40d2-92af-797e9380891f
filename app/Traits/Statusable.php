<?php

declare(strict_types=1);

namespace App\Traits;

use App\Enums\OrderItemStatus;

trait Statusable
{
    public function generateStatus(array $statusCounts): OrderItemStatus
    {
        $totalItems = array_sum($statusCounts);
        $activeItems = $totalItems
            - ($statusCounts[OrderItemStatus::Rejected->value] ?? 0)
            - ($statusCounts[OrderItemStatus::PlacementFailed->value] ?? 0)
            - ($statusCounts[OrderItemStatus::Cancelled->value] ?? 0);

        // If all items are cancelled or rejected
        if (($statusCounts[OrderItemStatus::Cancelled->value] ?? 0) + ($statusCounts[OrderItemStatus::Rejected->value] ?? 0) === $totalItems) {
            return OrderItemStatus::Cancelled;
        }

        // If all items are pending or placement failed
        if (($statusCounts[OrderItemStatus::Pending->value] ?? 0) + ($statusCounts[OrderItemStatus::PlacementFailed->value] ?? 0) === $totalItems) {
            return OrderItemStatus::Pending;
        }

        // If all items are delivered
        if (($statusCounts[OrderItemStatus::Delivered->value] ?? 0) === $activeItems) {
            return OrderItemStatus::Delivered;
        }

        // If some items are delivered and others are shipped or pending
        if (($statusCounts[OrderItemStatus::Delivered->value] ?? 0) > 0) {
            $remainingItems = $activeItems - ($statusCounts[OrderItemStatus::Delivered->value] ?? 0);
            $shippedOrPendingItems = ($statusCounts[OrderItemStatus::Shipped->value] ?? 0)
                + ($statusCounts[OrderItemStatus::Pending->value] ?? 0)
                + ($statusCounts[OrderItemStatus::Accepted->value] ?? 0)
                + ($statusCounts[OrderItemStatus::Backordered->value] ?? 0);
            if ($remainingItems === $shippedOrPendingItems) {
                return OrderItemStatus::PartiallyDelivered;
            }
        }

        // If all active items are shipped
        if (($statusCounts[OrderItemStatus::Shipped->value] ?? 0) === $activeItems) {
            return OrderItemStatus::Shipped;
        }

        // If some items are shipped and others are pending or accepted
        if (($statusCounts[OrderItemStatus::Shipped->value] ?? 0) > 0) {
            $remainingActiveItems = $activeItems - ($statusCounts[OrderItemStatus::Shipped->value] ?? 0);
            $pendingOrAcceptedItems = ($statusCounts[OrderItemStatus::Pending->value] ?? 0)
                + ($statusCounts[OrderItemStatus::Accepted->value] ?? 0)
                + ($statusCounts[OrderItemStatus::Backordered->value] ?? 0);
            if ($remainingActiveItems === $pendingOrAcceptedItems) {
                return OrderItemStatus::PartiallyShipped;
            }
        }

        // If items are pending or accepted
        if (($statusCounts[OrderItemStatus::Pending->value] ?? 0) > 0 || ($statusCounts[OrderItemStatus::Accepted->value] ?? 0) > 0) {
            return OrderItemStatus::Processing;
        }

        // Default to pending
        return OrderItemStatus::Pending;
    }

    public function generateOptionMapping(): array
    {
        return [
            OrderItemStatus::Pending->value => 'info',
            OrderItemStatus::PlacementFailed->value => 'warning',
            OrderItemStatus::Rejected->value => 'danger',
            OrderItemStatus::Accepted->value => 'info',
            OrderItemStatus::Processing->value => 'info',
            OrderItemStatus::Backordered->value => 'warning',
            OrderItemStatus::Shipped->value => 'success',
            OrderItemStatus::PartiallyShipped->value => 'info',
            OrderItemStatus::Delivered->value => 'success',
            OrderItemStatus::PartiallyDelivered->value => 'info',
            OrderItemStatus::Cancelled->value => 'danger',
            OrderItemStatus::Returned->value => 'danger',
        ];
    }
}
