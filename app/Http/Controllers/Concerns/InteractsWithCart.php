<?php

declare(strict_types=1);

namespace App\Http\Controllers\Concerns;

use App\Models\Cart;
use App\Models\Clinic;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait InteractsWithCart
{
    /**
     * Get the clinic's cart.
     */
    protected function cart(Clinic $clinic): Cart
    {
        $applyProductOfferConditions = function ($query) use ($clinic) {
            return $query->withVendorConnected([$clinic->id])
                ->withClinic($clinic->id)
                ->withLastClinicOrderItem($clinic->id)
                ->withClinicCartItem($clinic->id)
                ->whereVendorIdIn($clinic->vendors->pluck('id')->unique()->values()->toArray())
                ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id));
        };

        return $clinic->cart()
            ->with([
                'items.productOffer' => function (BelongsTo $query) use ($applyProductOfferConditions) {
                    return $query->with([
                        'vendor',
                        'product' => fn ($query) => $query->with([
                            'productOffers' => $applyProductOfferConditions,
                        ]),
                    ])->tap($applyProductOfferConditions);
                },
                'items.vendor',
            ])
            ->firstOrCreate();
    }
}
