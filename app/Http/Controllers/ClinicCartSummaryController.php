<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Controllers\Concerns\InteractsWithCart;
use App\Http\Resources\CartSummary;
use App\Models\Clinic;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class ClinicCartSummaryController extends Controller
{
    use AuthorizesRequests, InteractsWithCart;

    /**
     * Get the cart summary for the given clinic.
     */
    public function show(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $cart = $this->cart($clinic);

        return CartSummary::make($cart)->response();
    }
}
