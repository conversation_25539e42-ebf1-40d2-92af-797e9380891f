<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics\Orders;

use App\Actions\Orders\PlaceOrder;
use App\Http\Controllers\Concerns\InteractsWithCart;
use App\Http\Controllers\Controller;
use App\Http\Requests\PlaceClinicOrderRequest;
use App\Http\Resources\Order;
use App\Models\Clinic;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class ClinicOrderController extends Controller
{
    use AuthorizesRequests, InteractsWithCart;

    /**
     * Store a newly created resource in storage.
     */
    public function store(
        PlaceClinicOrderRequest $request,
        PlaceOrder $action,
        Clinic $clinic
    ): JsonResponse {
        $cart = $this->cart($clinic);

        $order = $action->handle($request->user(), $cart, $request->validated());

        return Order::make($order->load([
            'items',
            'billingAddress',
            'shippingAddress',
            'promotions.promotion',
        ]))
            ->response()
            ->setStatusCode(JsonResponse::HTTP_CREATED);
    }
}
