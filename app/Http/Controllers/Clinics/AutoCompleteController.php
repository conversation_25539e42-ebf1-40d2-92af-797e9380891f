<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Actions\Products\AutoComplete;
use App\Http\Controllers\Controller;
use App\Models\Clinic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class AutoCompleteController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly AutoComplete $engine,
    ) {
        //
    }

    public function index(Request $request, Clinic $clinic): JsonResponse
    {
        $products = $this->engine->handle(
            $clinic,
            $request->string('query')->toString(),
        );

        return response()->json([
            'data' => $products,
        ]);
    }
}
