<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Concerns\InteractsWithCart;
use App\Http\Controllers\Controller;
use App\Http\Requests\Clinics\AddCartItemRequest;
use App\Http\Resources\Cart\Cart;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\Promotion\Services\CartPromotionEligibilityService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

final class CartController extends Controller
{
    use AuthorizesRequests, InteractsWithCart;

    /**
     * Get the clinic's cart.
     */
    public function show(Clinic $clinic): JsonResponse
    {
        $this->authorize('view', $clinic);

        $cart = $this->cart($clinic);

        return Cart::make($cart)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }

    /**
     * Add/Delete item/s in the clinic's cart.
     */
    public function create(AddCartItemRequest $request, Clinic $clinic): JsonResponse
    {
        $items = $request->validated('items');

        DB::transaction(function () use ($items, $clinic) {
            foreach ($items as $item) {
                $productOffer = ProductOffer::with('vendor')
                    ->withVendorConnected([$clinic->id])
                    ->withClinic($clinic->id)
                    ->withLastClinicOrderItem($clinic->id)
                    ->withClinicCartItem($clinic->id)
                    ->whereVendorIdIn($clinic->vendors->pluck('id')->unique()->values()->toArray())
                    ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                    ->findOrFail($item['product_offer_id']);

                $cart = $this->cart($clinic);

                if ($item['quantity'] === 0) {
                    $cart->items()->where('product_offer_id', $productOffer->id)->delete();

                    continue;
                }

                $cart->addItem($productOffer, $item['quantity'], null, $item['notes'] ?? null);
            }
        });

        return Cart::make($this->cart($clinic))
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }

    /**
     * Get promotion eligibility for the clinic's cart.
     */
    public function promotions(Clinic $clinic, CartPromotionEligibilityService $promotionEligibilityService): JsonResponse
    {
        $this->authorize('view', $clinic);

        $cart = $this->cart($clinic);

        $eligibilityResult = $promotionEligibilityService->checkCartEligibility($cart);

        return response()->json($eligibilityResult);
    }

    /**
     * Clean the clinic's cart.
     */
    public function destroy(Clinic $clinic): JsonResponse
    {
        $cart = $this->cart($clinic);

        $cart->clean();

        return Cart::make($this->cart($clinic))
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }
}
