<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Concerns\InteractsWithCart;
use App\Http\Controllers\Controller;
use App\Http\Requests\Clinics\UpdateCartItemRequest;
use App\Http\Resources\Cart\Cart;
use App\Models\CartItem;
use App\Models\Clinic;
use App\Models\ProductOffer;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class CartItemController extends Controller
{
    use AuthorizesRequests, InteractsWithCart;

    /**
     * Update the cart item.
     */
    public function update(UpdateCartItemRequest $request, Clinic $clinic, CartItem $item): JsonResponse
    {
        $product = ProductOffer::with('vendor')
            ->withVendorConnected([$clinic->id])
            ->withClinic($clinic->id)
            ->withLastClinicOrderItem($clinic->id)
            ->withClinicCartItem($clinic->id)
            ->whereVendorIdIn($clinic->vendors->pluck('id')->unique()->values()->toArray())
            ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
            ->findOrFail($request->validated('product_offer_id'));

        $cart = $this->cart($clinic);

        $quantity = $request->validated('quantity') ?? $item->quantity;
        $notes = $request->validated('notes') ?? $item->notes;

        // removes the item from the cart
        $cart->updateItem($item->productOffer, ['quantity' => 0]);

        // adds the new product to the cart
        $cart->addItem($product, $quantity, null, $notes);

        return Cart::make($this->cart($clinic))
            ->response()
            ->setStatusCode(JsonResponse::HTTP_OK);
    }
}
