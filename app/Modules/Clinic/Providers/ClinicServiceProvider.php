<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Providers;

use Illuminate\Support\Facades\Request;
use Illuminate\Support\ServiceProvider;

final class ClinicServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerRoutes();

        $this->registerRequestMacros();
    }

    protected function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
    }

    private function registerRequestMacros(): void
    {
        Request::macro('clinicId', fn () => Request::header('Highfive-Clinic') ?? Request::route('clinic')?->id);
    }
}
