<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Providers;

use App\Modules\Gpo\Nova\GpoUser;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class GpoServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
    }

    public function register(): void
    {
        Nova::resources([
            GpoUser::class,
        ]);
    }
}
