<?php

declare(strict_types=1);

use App\Modules\Gpo\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Route;

Route::prefix('api/gpo')->group(function () {
    Route::post('login', [AuthController::class, 'login']);

    Route::middleware('auth:gpo')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);
    });
});
