<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models;

use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\Factories\GpoAccountFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

final class GpoAccount extends Model
{
    use HasFactory;
    use HasUuids;

    protected $with = ['details'];

    public static function newFactory(): GpoAccountFactory
    {
        return GpoAccountFactory::new();
    }

    public function getForeignKey(): string
    {
        return 'gpo_account_id';
    }

    public function details(): HasOne
    {
        return $this->hasOne(GpoAccountDetails::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(GpoUser::class, 'account_id');
    }

    public function members(): HasMany
    {
        return $this->hasMany(ClinicAccount::class);
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(GpoInvitation::class);
    }

    public function recommendedVendors(): BelongsToMany
    {
        return $this->belongsToMany(
            Vendor::class,
            'gpo_recommended_vendors',
            'gpo_account_id',
            'vendor_id',
        )->withPivot('order')
            ->withTimestamps();
    }

    public function recommendedProducts(): BelongsToMany
    {
        return $this->belongsToMany(
            ProductOffer::class,
            'gpo_recommended_products',
            'gpo_account_id',
            'product_offer_id',
        )->withTimestamps();
    }
}
