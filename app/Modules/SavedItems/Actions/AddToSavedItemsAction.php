<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Models\CartItem;
use App\Modules\SavedItems\Models\SavedItem;

final class AddToSavedItemsAction
{
    public function handle(string $cartItemId): SavedItem
    {
        $cartItem = CartItem::with(['cart.clinic'])->findOrFail($cartItemId);

        $clinic = $cartItem->cart->clinic;
        $productOffer = $cartItem->productOffer;
        $quantity = $cartItem->quantity;

        $savedItem = SavedItem::firstOrNew([
            'clinic_id' => $clinic->id,
            'product_offer_id' => $productOffer->id,
        ], [
            'quantity' => 0,
        ]);

        if ($savedItem->exists) {
            $savedItem->quantity += $quantity;
        } else {
            $savedItem->quantity = $quantity;
        }

        $savedItem->save();

        $cartItem->delete();

        return $savedItem;
    }
}
