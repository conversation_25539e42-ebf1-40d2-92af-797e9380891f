<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Http;

use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Services\Vendor\Responses\VendorResponse;
use Illuminate\Http\Client\PendingRequest;

abstract class BaseHttpClient
{
    protected PendingRequest $client;

    protected ResponseHandler $responseHandler;

    public function __construct()
    {
        $this->responseHandler = app(ResponseHandler::class);
    }

    abstract protected function configureClient(): void;

    final public function post(
        string $endpoint,
        array $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        $session->logSuccessEvent(
            $integrationEventAction.'_initiated',
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => 'POST',
                'body' => $data,
            ]
        );

        return $this->responseHandler->handle(
            $this->client->post($endpoint, $data),
            $session,
            $integrationEventAction,
            $subject
        );
    }

    final public function get(
        string $endpoint,
        array $query,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        $session->logSuccessEvent(
            $integrationEventAction.'_initiated',
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => 'GET',
                'body' => $query,
            ]
        );

        return $this->responseHandler->handle(
            $this->client->get($endpoint, $query),
            $session,
            $integrationEventAction,
            $subject
        );
    }

    final public function put(
        string $endpoint,
        array $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        $session->logSuccessEvent(
            $integrationEventAction.'_initiated',
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => 'PUT',
                'body' => $data,
            ]
        );

        return $this->responseHandler->handle(
            $this->client->put($endpoint, $data),
            $session,
            $integrationEventAction,
            $subject
        );
    }

    final public function delete(
        string $endpoint,
        array $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        $session->logSuccessEvent(
            $integrationEventAction.'_initiated',
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => 'DELETE',
                'body' => $data,
            ]
        );

        return $this->responseHandler->handle(
            $this->client->delete($endpoint, $data),
            $session,
            $integrationEventAction,
            $subject
        );
    }

    final public function patch(
        string $endpoint,
        array $data,
        IntegrationSession $session,
        string $integrationEventAction,
        $subject
    ): VendorResponse {
        $session->logSuccessEvent(
            $integrationEventAction.'_initiated',
            $subject,
            [
                'endpoint' => $endpoint,
                'method' => 'PATCH',
                'body' => $data,
            ]
        );

        return $this->responseHandler->handle(
            $this->client->patch($endpoint, $data),
            $session,
            $integrationEventAction,
            $subject
        );
    }

    final public function asJson(): self
    {
        $this->client->asJson();

        return $this;
    }

    final public function contentType(string $contentType): self
    {
        $this->client->contentType($contentType);

        return $this;
    }

    final public function accept(string $contentType): self
    {
        $this->client->accept($contentType);

        return $this;
    }

    final public function headers(array $headers): self
    {
        $this->client->withHeaders($headers);

        return $this;
    }
}
