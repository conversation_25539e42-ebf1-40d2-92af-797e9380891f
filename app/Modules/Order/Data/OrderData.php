<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Enums\OrderItemStatus;
use App\Models\Order;
use Brick\Money\Money;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

final class OrderData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $orderNumber,
        #[MapInputName('created_at')]
        public readonly string $date,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $totalPrice,
        public readonly OrderItemStatus $status,
        public readonly string $downloadChecklistUrl,
        public readonly ?string $downloadInvoicesUrl,
        #[DataCollectionOf(VendorOrderData::class)]
        public readonly Collection|Lazy $vendorOrders,
        public readonly int|Lazy $itemsCount,
        public readonly int|Lazy $vendorsCount,
        #[DataCollectionOf(OrderPromotionData::class)]
        public readonly Collection|Lazy $promotions,
    ) {}

    public static function fromModel(Order $order): self
    {
        return self::from([
            'id' => $order->id,
            'orderNumber' => $order->order_number,
            'date' => $order->created_at->format('Y-m-d'),
            'totalPrice' => $order->total_price,
            'status' => $order->status,
            'downloadChecklistUrl' => $order->download_checklist_url,
            'downloadInvoicesUrl' => $order->download_invoices_url,
            'vendorOrders' => Lazy::create(fn () => VendorOrderData::collect($order->suborders)),
            'itemsCount' => Lazy::create(fn () => $order->items_count),
            'vendorsCount' => Lazy::create(fn () => $order->vendors_count),
            'promotions' => Lazy::create(fn () => OrderPromotionData::collect($order->promotions)),
        ]);
    }
}
