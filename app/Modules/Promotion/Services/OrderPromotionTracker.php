<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Services;

use App\Models\Order;
use App\Models\OrderPromotion;
use App\Models\ProductOffer;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionRule;
use Illuminate\Support\Collection;
use Log;

final class OrderPromotionTracker
{
    /**
     * Track and save promotion data for an order
     */
    public function trackAppliedPromotions(Order $order, Collection $appliedPromotions, Context $context): void
    {
        foreach ($appliedPromotions as $promotionData) {
            $this->savePromotionSnapshot($order, $promotionData['promotion'], $promotionData['rule'], $context);
        }
    }

    /**
     * Save a snapshot of the promotion at the time of order
     */
    private function savePromotionSnapshot(Order $order, Promotion $promotion, PromotionRule $rule, Context $context): void
    {
        // Check if this promotion has already been tracked for this order
        $existingRecord = OrderPromotion::where('order_id', $order->id)
            ->where('promotion_id', $promotion->id)
            ->first();

        if ($existingRecord) {
            Log::warning('OrderPromotion record already exists', [
                'orderId' => $order->id,
                'promotionId' => $promotion->id,
                'existingRecordId' => $existingRecord->id,
            ]);

            return;
        }

        $triggeringItems = $this->getTriggeringItems($promotion, $context);
        $appliedRules = $this->getAppliedRules($rule);
        $appliedBenefits = $this->getAppliedBenefits($rule, $context);

        OrderPromotion::create([
            'order_id' => $order->id,
            'promotion_id' => $promotion->id,
            'triggering_items' => $triggeringItems,
            'applied_rules' => $appliedRules,
            'applied_benefits' => $appliedBenefits,
        ]);
    }

    /**
     * Get triggering items that caused the promotion to apply
     */
    private function getTriggeringItems(Promotion $promotion, Context $context): array
    {
        $triggeringItems = [];

        // Get items from context that match promotion's product offers
        if ($context->cart) {
            foreach ($context->cart as $productId => $quantity) {
                // Find product offer for this product in the promotion
                $productOffer = $promotion->productOffers
                    ->where('product_id', $productId)
                    ->first();

                if ($productOffer) {
                    $triggeringItems[] = [
                        'product_offer_id' => $productOffer->id,
                        'product_id' => $productId,
                        'quantity' => $quantity,
                    ];
                }
            }
        }

        return $triggeringItems;
    }

    /**
     * Get applied rules data
     */
    private function getAppliedRules(PromotionRule $rule): array
    {
        return [
            [
                'rule_id' => $rule->id,
                'priority' => $rule->priority,
                'conditions' => $rule->conditions->map(function ($condition) {
                    return [
                        'id' => $condition->id,
                        'type' => $condition->type->value,
                        'description' => $this->getConditionDescription($condition),
                        'config' => $condition->config,
                    ];
                })->toArray(),
                'actions' => $rule->actions->map(function ($action) {
                    return [
                        'id' => $action->id,
                        'type' => $action->type->value,
                        'description' => $this->getActionDescription($action),
                        'config' => $action->config,
                    ];
                })->toArray(),
            ],
        ];
    }

    /**
     * Get applied benefits data
     */
    private function getAppliedBenefits(PromotionRule $rule, Context $context): array
    {
        $benefits = [];

        foreach ($rule->actions as $action) {
            switch ($action->type->value) {
                case ActionType::GiveFreeProduct->value:
                    $productOfferId = $action->config['free_product_offer_id'] ?? null;
                    $productOffer = null;

                    if ($productOfferId) {
                        $productOfferModel = ProductOffer::with(['product', 'vendor', 'clinics'])
                            ->find($productOfferId);

                        if ($productOfferModel) {
                            // Get clinic-specific pricing if available
                            $clinicPrice = null;
                            $price = $productOfferModel->price;

                            // Try to get clinic price from context if available
                            if ($context->clinicId && $productOfferModel->clinics) {
                                $clinic = $productOfferModel->clinics->where('id', $context->clinicId)->first();
                                $clinicPrice = $clinic?->pivot?->price;
                            }

                            $productOffer = [
                                'id' => $productOfferModel->id,
                                'name' => $productOfferModel->name,
                                'price' => $price ? round($price / 100, 2) : null,
                                'clinicPrice' => $clinicPrice ? round($clinicPrice / 100, 2) : null,
                                'imageUrl' => $productOfferModel->image_url,
                            ];
                        }
                    }

                    $benefits[] = [
                        'type' => ActionType::GiveFreeProduct->value,
                        'product_offer_id' => $productOfferId,
                        'product_offer' => $productOffer,
                        'quantity' => $action->config['quantity'] ?? 0,
                        'message' => $action->config['message'] ?? null,
                    ];
                    break;

                case ActionType::UpdateRebateEstimate->value:
                    $benefits[] = [
                        'type' => 'rebate',
                        'percentage' => $action->config['rebate_percent'] ?? 0,
                        'description' => "{$action->config['rebate_percent']}% rebate on qualifying purchases",
                    ];
                    break;

                default:
                    $benefits[] = [
                        'type' => $action->type->value,
                        'description' => 'Unknown benefit type',
                        'config' => $action->config,
                    ];
                    break;
            }
        }

        return $benefits;
    }

    /**
     * Get condition description
     */
    private function getConditionDescription($condition): string
    {
        switch ($condition->type->value) {
            case ConditionType::MinimumQuantity->value:
                return ConditionType::MinimumQuantity->label().": {$condition->config['quantity']}";
            case ConditionType::MinimumSpendAmount->value:
                return ConditionType::MinimumSpendAmount->label().": \${$condition->config['minimum_spend_amount']}";
            case ConditionType::MinimumYearOverYearSpendGrowthPercent->value:
                return ConditionType::MinimumYearOverYearSpendGrowthPercent->label().": {$condition->config['minimum_year_over_year_spend_growth_percent']}%";
            default:
                return "Condition: {$condition->type->value}";
        }
    }

    /**
     * Get action description
     */
    private function getActionDescription($action): string
    {
        switch ($action->type->value) {
            case ActionType::GiveFreeProduct->value:
                return ActionType::GiveFreeProduct->label();
            case ActionType::UpdateRebateEstimate->value:
                return ActionType::UpdateRebateEstimate->label();
            default:
                return "Action: {$action->type->value}";
        }
    }
}
