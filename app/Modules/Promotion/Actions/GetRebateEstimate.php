<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Actions;

use App\Actions\Products\GetRebateEstimateByProductOffer;
use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Data\RebateEstimateData;
use App\Modules\Promotion\Data\SuggestedProductOfferData;
use App\Modules\Promotion\Data\VendorData;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\RebateEstimate;
use App\Modules\Promotion\Queries\RebateEstimateQuery;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

final readonly class GetRebateEstimate
{
    public function __construct(
        private GetRebateEstimateByProductOffer $getRebateEstimateByProductOffer
    ) {}

    public function handle(string $clinicId, string $gpoId)
    {
        $today = Carbon::today();
        $clinic = Clinic::findOrFail($clinicId);
        $query = RebateEstimate::query()
            ->with(['promotion', 'promotion.vendor'])
            ->where('clinic_id', $clinicId)
            ->whereHas('promotion', function (Builder $query) use ($gpoId, $today) {
                $query->where('promotionable_type', GpoAccount::class)
                    ->where('promotionable_id', $gpoId)
                    ->where('type', PromotionType::Rebate)
                    ->where('status', PromotionStatus::Active)
                    ->where('started_at', '<=', $today)
                    ->where('ended_at', '>=', $today);
            });

        $estimates = RebateEstimateQuery::for($query)->get();

        return $estimates->map(function (RebateEstimate $estimate) use ($clinicId, $clinic) {
            $suggestedOffers = $estimate->promotion->productOffers()
                ->with(['vendor', 'product', 'clinics'])
                ->when($clinic->account?->gpo, fn ($query) => $query->withExistsGpo($clinic->account->gpo->id))
                ->whereHas('orderItems', function ($q) use ($clinicId) {
                    $q->whereHas('order', function ($q) use ($clinicId) {
                        $q->where('clinic_id', $clinicId)
                            ->where('created_at', '>=', now()->subMonths(12))
                            ->where('created_at', '<=', now());
                    });
                })
                ->withSum(['orderItems' => function ($q) use ($clinicId) {
                    $q->whereHas('order', function ($q) use ($clinicId) {
                        $q->where('clinic_id', $clinicId)
                            ->where('created_at', '>=', now()->subMonths(12))
                            ->where('created_at', '<=', now());
                    });
                }], 'total_price')
                ->orderByDesc('order_items_sum_total_price')
                ->limit(3)
                ->get();

            return RebateEstimateData::from([
                'id' => $estimate->id,
                'promotion' => PromotionData::from($estimate->promotion),
                'currentSpendAmount' => Money::ofMinor($estimate->current_spend_amount, 'USD'),
                'currentRebatePercent' => $estimate->current_rebate_percent,
                'nextTierMinimumSpendAmountThreshold' => $estimate->next_tier_minimum_spend_amount_threshold
                    ? Money::ofMinor($estimate->next_tier_minimum_spend_amount_threshold, 'USD')
                    : null,
                'nextTierRebatePercent' => $estimate->next_tier_rebate_percent,
                'estimatedRebateAmount' => Money::ofMinor($estimate->estimated_rebate_amount, 'USD'),
                'suggestedProductOffers' => $suggestedOffers->map(function ($offer) use ($clinicId) {
                    $clinicPrice = null;
                    $clinic = $offer->clinics->where('id', $clinicId)->first();
                    if ($clinic && $clinic->pivot && $clinic->pivot->price) {
                        $clinicPrice = Money::ofMinor($clinic->pivot->price, 'USD');
                    }

                    return SuggestedProductOfferData::from([
                        'id' => $offer->id,
                        'name' => $offer->name,
                        'imageUrl' => $offer->image_url,
                        'vendorSku' => $offer->vendor_sku,
                        'price' => $offer->price ? Money::ofMinor($offer->price, 'USD') : null,
                        'clinicPrice' => $clinicPrice,
                        'stockStatus' => $offer->stock_status,
                        'vendor' => VendorData::from($offer->vendor),
                        'orderItemsSumTotalPrice' => $offer->order_items_sum_total_price,
                        'increments' => $offer->increments,
                        'product' => $offer->product,
                        'isRecommended' => $offer->is_recommended ?? false,
                        'rebatePercent' => $this->getRebateEstimateByProductOffer->handle($clinicId, $offer->id)?->current_rebate_percent,
                    ]);
                }),
            ]);
        });
    }
}
