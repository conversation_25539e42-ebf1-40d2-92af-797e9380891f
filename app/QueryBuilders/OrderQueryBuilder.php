<?php

declare(strict_types=1);

namespace App\QueryBuilders;

use Illuminate\Database\Eloquent\Builder;

final class OrderQueryBuilder extends Builder
{
    /**
     * Filter orders by a suborder that matches the given vendor ID and external ID.
     */
    public function whereHasSubOrderFromVendor(string $vendorId, string $externalId): self
    {
        return $this->whereHas(
            'suborders',
            fn (Builder $query) => $query->where('vendor_id', $vendorId)->where('external_id', $externalId)
        );
    }
}
