<?php

declare(strict_types=1);

namespace App\Support;

use Illuminate\Support\Facades\DB;

final class SequenceNumber
{
    /**
     * Get the next sequence number for the given model.
     */
    public static function next(string $prefix): string
    {
        $format = fn (string $prefix, int $value): string => sprintf('%s%06d', mb_strtoupper($prefix), $value);

        return DB::transaction(function () use ($prefix, $format) {
            $sequence = DB::table('sequences')
                ->where(
                    'prefix',
                    $prefix,
                )
                ->lockForUpdate()
                ->first();

            if (is_null($sequence)) {
                DB::table('sequences')->insert([
                    'prefix' => $prefix,
                    'sequential' => 1,
                ]);

                return $format($prefix, 1);
            }

            $next = $sequence->sequential + 1;

            DB::table('sequences')->where(
                'prefix',
                $prefix,
            )->update(['sequential' => $next]);

            return $format($prefix, $next);
        });
    }
}
