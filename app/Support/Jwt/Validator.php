<?php

declare(strict_types=1);

namespace App\Support\Jwt;

use App\Support\Jwt\Exceptions\InvalidTokenException;
use Exception;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\UnencryptedToken;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint\LooseValidAt;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint\RelatedTo;
use <PERSON><PERSON><PERSON>cci\JWT\Validation\Constraint\SignedWith;
use Symfony\Component\Clock\NativeClock;

final class Validator
{
    private readonly NativeClock $clock;

    public function __construct(
        private readonly Configuration $config,
        ?NativeClock $clock = null,
    ) {
        $this->clock = $clock ?? new NativeClock();
    }

    public function decode(string $token): UnencryptedToken
    {
        try {
            return $this->config->parser()->parse($token);
        } catch (Exception $e) {
            throw new InvalidTokenException();
        }
    }

    public function validate(string $token, array $constraints = []): bool
    {
        $parsedToken = $this->decode($token);
        if (is_null($parsedToken)) {
            return false;
        }

        $defaultConstraints = [
            new SignedWith($this->config->signer(), $this->config->verificationKey()),
            new LooseValidAt($this->clock),
        ];

        return $this->config->validator()->validate(
            $parsedToken,
            ...[...$defaultConstraints, ...$constraints]
        );
    }

    public function constraintForSubject(string $subject): RelatedTo
    {
        return new RelatedTo($subject);
    }
}
