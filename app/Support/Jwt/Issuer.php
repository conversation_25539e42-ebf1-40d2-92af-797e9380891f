<?php

declare(strict_types=1);

namespace App\Support\Jwt;

use DateInterval;
use DateTimeImmutable;
use <PERSON><PERSON><PERSON>cci\JWT\Configuration;

final class Issuer
{
    public function __construct(private readonly Configuration $config) {}

    public function issue(
        string $id,
        string $subject,
        array $claims = [],
        ?DateTimeImmutable $expiresAt = null,
    ): string {
        $now = new DateTimeImmutable();

        $expiresAt = $expiresAt ?? $now->add(new DateInterval('P7D'));

        $builder = $this->config->builder()
            ->identifiedBy($id)
            ->relatedTo($subject)
            ->issuedAt($now)
            ->canOnlyBeUsedAfter($now)
            ->expiresAt($expiresAt);

        foreach ($claims as $key => $value) {
            $builder = $builder->withClaim($key, $value);
        }

        return $builder->getToken(
            $this->config->signer(),
            $this->config->signingKey()
        )->toString();
    }
}
