<?php

declare(strict_types=1);

namespace App\Support\Jwt;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;

final class JwtServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton('jwt.config', function () {
            $key = config('app.key');

            if (str_starts_with($key, 'base64:')) {
                $key = base64_decode(mb_substr($key, 7));
            }

            return Configuration::forSymmetricSigner(new Sha256(), InMemory::plainText($key));
        });

        $this->app->singleton('jwt.issuer', fn ($app) => new Issuer($app['jwt.config']));

        $this->app->singleton('jwt.validator', fn ($app) => new Validator($app['jwt.config']));
    }
}
