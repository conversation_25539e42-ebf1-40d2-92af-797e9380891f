<?php

declare(strict_types=1);

namespace App\Support\Facades;

use DateTimeImmutable;
use Illuminate\Support\Facades\Facade;
use <PERSON><PERSON><PERSON><PERSON>\JWT\UnencryptedToken;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint\RelatedTo;

final class Jwt extends Facade
{
    public static function issue(
        string $id,
        string $subject,
        array $claims = [],
        ?DateTimeImmutable $expiresAt = null,
    ): string {
        return app('jwt.issuer')->issue($id, $subject, $claims, $expiresAt);
    }

    /**
     * @throws \App\Support\Jwt\Exceptions\InvalidTokenException
     */
    public static function decode(string $token): UnencryptedToken
    {
        return app('jwt.validator')->decode($token);
    }

    public static function validate(string $token, array $constraints = []): bool
    {
        return app('jwt.validator')->validate($token, $constraints);
    }

    public static function constraintForSubject(string $subject): RelatedTo
    {
        return app('jwt.validator')->constraintForSubject($subject);
    }

    protected static function getFacadeAccessor(): string
    {
        return 'jwt';
    }
}
