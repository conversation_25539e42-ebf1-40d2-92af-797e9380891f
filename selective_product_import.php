<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = new Application(realpath(__DIR__));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Starting selective product import...\n";

// Get top manufacturers with their product counts
$manufacturers = DB::select("
    SELECT pm.id, pm.name, COUNT(p.id) as product_count 
    FROM product_manufacturers pm 
    LEFT JOIN products p ON pm.id = p.product_manufacturer_id 
    WHERE p.deleted_at IS NULL 
    GROUP BY pm.id, pm.name 
    HAVING COUNT(p.id) > 0
    ORDER BY product_count DESC 
    LIMIT 20
");

echo "Found " . count($manufacturers) . " manufacturers\n";

$totalIndexed = 0;

foreach ($manufacturers as $manufacturer) {
    echo "Processing manufacturer: {$manufacturer->name} ({$manufacturer->product_count} products)\n";
    
    // Get up to 100 products for this manufacturer
    $products = DB::select("
        SELECT id 
        FROM products 
        WHERE product_manufacturer_id = ? 
        AND deleted_at IS NULL 
        ORDER BY created_at DESC 
        LIMIT 100
    ", [$manufacturer->id]);
    
    echo "  Selected " . count($products) . " products for indexing\n";
    
    // Index these products in small batches
    $productIds = array_column($products, 'id');
    $chunks = array_chunk($productIds, 10);
    
    foreach ($chunks as $chunk) {
        $productModels = \App\Models\Product::whereIn('id', $chunk)->get();
        
        foreach ($productModels as $product) {
            try {
                $product->searchable();
                $totalIndexed++;
                echo "  Indexed product: {$product->name}\n";
            } catch (Exception $e) {
                echo "  Error indexing product {$product->id}: " . $e->getMessage() . "\n";
            }
        }
        
        // Small delay to prevent overwhelming the system
        usleep(100000); // 0.1 second
    }
    
    echo "  Completed manufacturer: {$manufacturer->name}\n\n";
}

echo "Selective import completed! Total products indexed: {$totalIndexed}\n";
