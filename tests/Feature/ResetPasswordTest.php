<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\Notification;
use Illuminate\Testing\TestResponse;

function requestPasswordReset(string $email): TestResponse
{
    return test()->postJson('/api/password-resets', compact('email'));
}

function resetPassword(string $token, string $email, string $password): TestResponse
{
    return test()->patchJson('/api/users/me/password', compact('token', 'email', 'password'));
}

beforeEach(function () {
    $this->user = User::factory()->create(['email' => '<EMAIL>']);
});

describe('password reset', function () {
    describe('requesting password reset', function () {
        it('successfully handles valid password reset request', function () {
            requestPasswordReset($this->user->email)
                ->assertCreated()
                ->assertJson(['status' => 'We have emailed your password reset link.']);

            $this->assertDatabaseHas('password_reset_tokens', ['email' => $this->user->email]);
        });

        it('handles password reset request with the user being authenticated', function () {
            $this->actingAs($this->user);

            requestPasswordReset($this->user->email)
                ->assertCreated();
        });

        it('handles invalid email for password reset request', function () {
            requestPasswordReset('<EMAIL>')
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });

        it('prevents duplicate password reset requests', function () {
            requestPasswordReset($this->user->email);

            requestPasswordReset($this->user->email)
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });

        it('sends a notification to the user', function () {
            Notification::fake();

            requestPasswordReset($this->user->email);

            Notification::assertSentTo($this->user, ResetPassword::class);
        });
    });

    describe('resetting password', function () {
        it('successfully resets password with valid token', function () {
            Notification::fake();

            requestPasswordReset($this->user->email);

            Notification::assertSentTo($this->user, ResetPassword::class, function (ResetPassword $notification) {
                resetPassword($notification->token, $this->user->email, 'new-password')
                    ->assertOk()
                    ->assertJson([
                        'id' => $this->user->id,
                        'name' => $this->user->name,
                        'email' => $this->user->email,
                        'account' => null,
                        'clinicId' => null,
                    ]);

                return true;
            });
        });

        it('successfully resets password with valid token even if the user is authenticated', function () {
            Notification::fake();

            $this->actingAs($this->user);

            requestPasswordReset($this->user->email);

            Notification::assertSentTo($this->user, ResetPassword::class, function (ResetPassword $notification) {
                resetPassword($notification->token, $this->user->email, 'new-password')
                    ->assertOk();

                return true;
            });
        });

        it('fails to reset password with invalid token', function () {
            resetPassword('invalid', $this->user->email, 'new-password')
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });
    });
});
