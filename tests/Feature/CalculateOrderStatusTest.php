<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Models\Order;
use App\Models\OrderItem;

function createOrderWithOrderItems(array $statuses): Order
{
    $order = Order::factory()->create();
    foreach ($statuses as $status) {
        OrderItem::factory()->for($order)->create(['status' => $status]);
    }

    return $order->fresh();
}

describe('calculate order status', function () {
    // Pending Cases
    it('sets status to pending when all items are pending', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Pending,
            OrderItemStatus::Pending,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Pending);
    });

    it('sets status to pending when all items are placement failed', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::PlacementFailed,
            OrderItemStatus::PlacementFailed,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Pending);
    });

    it('sets status to pending when items are mixed pending and placement failed', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Pending,
            OrderItemStatus::PlacementFailed,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Pending);
    });

    // Processing Cases
    it('sets status to processing when all items are accepted', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Accepted,
            OrderItemStatus::Accepted,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Processing);
    });

    it('sets status to processing when items are mixed accepted and pending', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Accepted,
            OrderItemStatus::Pending,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Processing);
    });

    it('sets status to processing when items have mixed states including accepted', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Accepted,
            OrderItemStatus::Pending,
            OrderItemStatus::PlacementFailed,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Processing);
    });

    // Partially Shipped Cases
    it('sets status to partially shipped when some items are shipped and others are pending', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Shipped,
            OrderItemStatus::Pending,
        ]);
        expect($order->status)->toBe(OrderItemStatus::PartiallyShipped);
    });

    it('sets status to partially shipped when items have multiple states including shipped', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Shipped,
            OrderItemStatus::Pending,
            OrderItemStatus::Accepted,
            OrderItemStatus::PlacementFailed,
        ]);
        expect($order->status)->toBe(OrderItemStatus::PartiallyShipped);
    });

    // Shipped Cases
    it('sets status to shipped when all active items are shipped', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Shipped,
            OrderItemStatus::Shipped,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Shipped);
    });

    it('sets status to delivered when all active items are delivered', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Delivered,
            OrderItemStatus::Delivered,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Delivered);
    });

    // Partially Delivered Cases
    it('sets status to partially delivered when some items are delivered and others are shipped', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Delivered,
            OrderItemStatus::Shipped,
        ]);
        expect($order->status)->toBe(OrderItemStatus::PartiallyDelivered);
    });

    it('sets status to partially delivered when some items are delivered and others are pending', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Delivered,
            OrderItemStatus::Pending,
        ]);
        expect($order->status)->toBe(OrderItemStatus::PartiallyDelivered);
    });

    it('sets status to partially delivered when items have multiple states including delivered', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Delivered,
            OrderItemStatus::Shipped,
            OrderItemStatus::Pending,
            OrderItemStatus::PlacementFailed,
        ]);
        expect($order->status)->toBe(OrderItemStatus::PartiallyDelivered);
    });

    // Delivered Cases
    it('sets status to delivered when all items are delivered', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Delivered,
            OrderItemStatus::Delivered,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Delivered);
    });

    // Cancelled Cases
    it('sets status to cancelled when all items are cancelled', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Cancelled,
            OrderItemStatus::Cancelled,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Cancelled);
    });

    it('sets status to cancelled when all items are rejected', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Rejected,
            OrderItemStatus::Rejected,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Cancelled);
    });

    it('sets status to cancelled when items are mixed cancelled and rejected', function () {
        $order = createOrderWithOrderItems([
            OrderItemStatus::Cancelled,
            OrderItemStatus::Rejected,
        ]);
        expect($order->status)->toBe(OrderItemStatus::Cancelled);
    });
});
