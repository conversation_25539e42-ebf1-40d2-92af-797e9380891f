<?php

declare(strict_types=1);

use App\Listeners\SendOrderPlacementFailedSlackNotification;
use App\Models\SubOrder;
use App\Modules\Order\Events\OrderPlacementFailed;
use App\Services\SlackNotificationService;
use Illuminate\Support\Facades\App;

it('sends a Slack notification when OrderPlacementFailed is handled', function () {
    $suborder = SubOrder::factory()->create();
    $event = new OrderPlacementFailed($suborder, new Exception('fail'));

    $mock = Mockery::mock('overload:'.SlackNotificationService::class);
    $mock->shouldReceive('sendOrderPlacementFailed')->once()->with($suborder);

    App::instance(SlackNotificationService::class, $mock);

    $listener = new SendOrderPlacementFailedSlackNotification();
    $listener->handle($event);
});
