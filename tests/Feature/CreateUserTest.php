<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Testing\TestResponse;

function createUser(array $data): TestResponse
{
    return test()->postJson('/api/users', $data);
}

function getValidUserData(array $overrides = []): array
{
    return array_merge([
        'name' => 'Sherlock Bones',
        'email' => '<EMAIL>',
        'password' => 'elementary-my-dear-watson',
    ], $overrides);
}

describe('user creation', function () {
    it('allows a user to be created', function () {
        $data = getValidUserData();

        createUser($data)
            ->assertCreated()
            ->assertJson([
                'name' => $data['name'],
                'email' => $data['email'],
            ]);

        expect(User::where('email', $data['email'])->exists())->toBeTrue();
    });

    it('requires the name field', function () {
        $data = getValidUserData(['name' => '']);

        createUser($data)
            ->assertUnprocessable()
            ->assertJsonValidationErrors('name');
    });

    it('requires a valid email address', function () {
        $data = getValidUserData(['email' => 'not-an-email']);

        createUser($data)
            ->assertUnprocessable()
            ->assertJsonValidationErrors('email');
    });

    it('requires a unique email address', function () {
        User::factory()->create(['email' => '<EMAIL>']);
        $data = getValidUserData(['email' => '<EMAIL>']);

        createUser($data)
            ->assertUnprocessable()
            ->assertJsonValidationErrors('email');
    });

    it('requires the password field', function () {
        $data = getValidUserData(['password' => '']);

        createUser($data)
            ->assertUnprocessable()
            ->assertJsonValidationErrors('password');
    });

    it('requires the password to be at least 8 characters long', function () {
        $data = getValidUserData(['password' => 'short']);

        createUser($data)
            ->assertUnprocessable()
            ->assertJsonValidationErrors('password');
    });

    it('hashes the password', function () {
        $data = getValidUserData();

        createUser($data);

        $user = User::where('email', $data['email'])->first();
        expect(Hash::check($data['password'], $user->password))->toBeTrue();
    });
});
