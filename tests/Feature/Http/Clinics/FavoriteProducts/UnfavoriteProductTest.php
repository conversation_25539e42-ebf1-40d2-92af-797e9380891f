<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();
    $this->product = $this->productOffer->product;
    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('requires the clinic to own the product before unfavoriting', function () {
    // The controller now returns 200 but with isFavorite: false for non-owned products
    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->deleteJson("/api/clinics/{$this->clinic->id}/favorite-products/{$this->product->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $this->product->id,
            'isFavorite' => false,
        ]);
});

it('handles repeated unfavorite requests gracefully', function () {
    $this->clinic->productOffers()->attach($this->productOffer, ['favorited_at' => null]);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->deleteJson("/api/clinics/{$this->clinic->id}/favorite-products/{$this->product->id}")
        ->assertOk();
});

it('successfully removes a product from favorites', function () {
    // Attach the product offer to the clinic with a specific price
    $this->clinic->productOffers()->attach($this->productOffer, [
        'price' => 9990,
        'favorited_at' => now(),
    ]);

    // Add the product to favorites
    $this->clinic->favoriteProducts()->attach($this->product->id, ['favorited_at' => now()]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->deleteJson("/api/clinics/{$this->clinic->id}/favorite-products/{$this->product->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $this->product->id,
            'isFavorite' => false,
        ]);

    // Verify that the product is no longer favorited
    $this->assertFalse(
        $this->clinic->favoriteProducts()->where('products.id', $this->product->id)->exists(),
        'Product should not be favorited after unfavoriting'
    );

    // Verify that the clinic-specific price is still maintained in the database
    $clinicOffer = $this->clinic->productOffers()->where('product_offer_id', $this->productOffer->id)->first();
    $this->assertNotNull($clinicOffer, 'Product offer should still be associated with clinic');
    $this->assertEquals(9990, $clinicOffer->pivot->price, 'Clinic-specific pricing should be maintained');
});

it('maintains clinic-specific pricing when unfavoriting', function () {

    // Ensure the product is associated with the product offer
    $this->product->productOffers()->save($this->productOffer);

    // Attach the product offer to the clinic with the specific price
    $this->clinic->productOffers()->attach($this->productOffer, [
        'price' => 9990,
    ]);

    // Add the product to favorites
    $this->clinic->favoriteProducts()->attach($this->product->id, ['favorited_at' => now()]);

    // Refresh relationships
    $this->product->load(['productOffers.vendor', 'productOffers.clinics']);
    $this->productOffer->load(['vendor', 'clinics']);
    $this->clinic->load(['vendors']);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->deleteJson("/api/clinics/{$this->clinic->id}/favorite-products/{$this->product->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $this->product->id,
            'isFavorite' => false,
        ]);

    // Verify that the product is no longer favorited
    $this->assertFalse(
        $this->clinic->favoriteProducts()->where('products.id', $this->product->id)->exists(),
        'Product should not be favorited after unfavoriting'
    );

    // Verify that the clinic-specific price is still maintained in the database
    $clinicOffer = $this->clinic->productOffers()->where('product_offer_id', $this->productOffer->id)->first();
    $this->assertNotNull($clinicOffer, 'Product offer should still be associated with clinic');
    $this->assertEquals(9990, $clinicOffer->pivot->price, 'Clinic-specific pricing should be maintained');
});
