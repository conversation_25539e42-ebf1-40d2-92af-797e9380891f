<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();

    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 10000,
    ]);
    $this->product = $this->productOffer->product;

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()
        ->create([
            'account_id' => $this->account->id,
        ]);
    $this->clinic = Clinic::factory()
        ->create([
            'clinic_account_id' => $this->account->id,
        ]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('successfully marks a clinic product as favorite', function () {
    $this->clinic->productOffers()->attach($this->productOffer, ['price' => 10000]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/favorite-products", [
            'product_id' => $this->product->id,
        ]);

    $response->assertCreated()
        ->assertJson([
            'id' => $this->product->id,
            'isFavorite' => true,
        ]);
});

it('successfully marks a product as favorite without requiring prior clinic ownership', function () {
    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/favorite-products", [
            'product_id' => $this->product->id,
        ]);

    $response->assertCreated()
        ->assertJson([
            'id' => $this->product->id,
            'isFavorite' => true,
        ]);

    // Verify the product is now favorited in the database
    $this->assertTrue(
        $this->clinic->favoriteProducts()->where('products.id', $this->product->id)->exists(),
        'Product should be marked as favorite in the database'
    );
});

it('handles repeated favorite requests gracefully', function () {
    $this->clinic->productOffers()->attach($this->productOffer, ['favorited_at' => now()]);

    $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/favorite-products", [
            'product_id' => $this->product->id,
        ])
        ->assertCreated();
});

it('maintains clinic-specific pricing when favoriting', function () {
    $this->clinic->productOffers()->attach($this->productOffer, [
        'price' => 9990,
        'favorited_at' => now(),
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/favorite-products", [
            'product_id' => $this->product->id,
        ]);

    $response->assertCreated()
        ->assertJson([
            'id' => $this->product->id,
            'isFavorite' => true,
        ]);

    // Verify the clinic-specific price is maintained in the database
    $clinicOffer = $this->clinic->productOffers()->where('product_offer_id', $this->productOffer->id)->first();
    $this->assertNotNull($clinicOffer, 'Product offer should be associated with clinic');
    $this->assertEquals(9990, $clinicOffer->pivot->price, 'Clinic-specific pricing should be maintained');
});
