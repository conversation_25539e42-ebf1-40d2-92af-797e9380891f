<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->product = $this->productOffer->product;
    $this->product->attributes()->createMany([
        ['name' => 'Color', 'value' => 'Red'],
        ['name' => 'Size', 'value' => 'Large'],
    ]);

    // Create another vendor and product offer for the same product
    $this->secondVendor = Vendor::factory()->enabled()->create();
    $this->additionalOffer = ProductOffer::factory()->for($this->secondVendor)->create([
        'product_id' => $this->product->id,
        'price' => 8990,
    ]);

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);

    // Attach product offers to clinic
    $this->clinic->productOffers()->attach($this->productOffer);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
    $this->clinic->productOffers()->attach($this->additionalOffer);

});

it('returns product data with correct structure', function () {
    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertOk()
        ->assertJsonStructure([
            'id',
            'name',
            'price',
            'clinicPrice',
            'lastOrderedAt',
            'lastOrderedQuantity',
            'isFavorite',
            'stockStatus',
            'vendorSku',
            'increments',
            'gpoSavings',
            'vendorSavings',
            'description',
            'manufacturer',
            'manufacturerSku',
            'isRecommended',
            'vendor' => [
                'id',
                'name',
                'imageUrl',
            ],
        ]);
})->skip();

it('shows clinic-specific price when customized', function () {
    $this->productOffer->update(['price' => 10000]);

    $this->clinic
        ->productOffers()
        ->syncWithoutDetaching([$this->productOffer->id => ['price' => 9990]]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertJson([
        'id' => $this->productOffer->id,
        'price' => 100.00,
        'clinicPrice' => 99.90,
    ]);
});

it('shows standard vendor price when no customization exists', function () {
    $this->productOffer->update(['price' => 10000]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertJson([
        'id' => $this->productOffer->id,
        'price' => 100.00,
    ]);
});

it('shows product as favorited when marked by clinic', function () {
    $this->clinic->favoriteProducts()->attach($this->product, ['favorited_at' => now()]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertJson(['isFavorite' => true]);
});

it('shows product as recommended when marked by gpo', function () {
    $gpo = GpoAccount::factory()->create();
    $gpo->recommendedProducts()->sync($this->productOffer);

    $this->account->update(['gpo_account_id' => $gpo->id]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertJson(['isRecommended' => true]);
});

it('shows product unrecommended when marked by default', function () {
    $gpo = GpoAccount::factory()->create();

    $this->account->update(['gpo_account_id' => $gpo->id]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertJson(['isRecommended' => false]);
});

it('shows product as unfavorited by default', function () {
    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertJson(['isFavorite' => false]);
});

it('returns 404 when the vendor is disabled', function () {
    $this->vendor->update(['is_enabled' => false]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertNotFound();
});

dataset('price_scenarios', [
    'both list price and clinic price' => [
        'list_price' => 199,
        'clinic_price' => 99,
        'expected_price' => 1.99,
        'expected_clinic_price' => 0.99,
    ],
    'only list price' => [
        'list_price' => 199,
        'clinic_price' => null,
        'expected_price' => 1.99,
        'expected_clinic_price' => null,
    ],
    'only clinic price' => [
        'list_price' => null,
        'clinic_price' => 99,
        'expected_price' => null,
        'expected_clinic_price' => 0.99,
    ],
    'no prices' => [
        'list_price' => null,
        'clinic_price' => null,
        'expected_price' => null,
        'expected_clinic_price' => null,
    ],
]);

it('handles different price scenarios correctly', function (
    ?int $list_price,
    ?int $clinic_price,
    ?float $expected_price,
    ?float $expected_clinic_price
) {
    $this->productOffer->update(['price' => $list_price]);

    if ($clinic_price !== null) {
        $this->clinic
            ->productOffers()
            ->syncWithoutDetaching([$this->productOffer->id => ['price' => $clinic_price]]);
    }

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/product-offers/{$this->productOffer->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $this->productOffer->id,
            'price' => $expected_price,
            'clinicPrice' => $expected_clinic_price,
        ]);
})->with('price_scenarios');

it('returns product with offers', function () {
    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}");

    $response->assertOk()
        ->assertJsonStructure([
            'id',
            'name',
            'imageUrl',
            'isFavorite',
            'manufacturer',
            'manufacturerSku',
            'description',
            'attributes',
            'offers' => [
                '*' => [
                    'id',
                    'vendor' => [
                        'id',
                        'name',
                        'imageUrl',
                    ],
                    'clinicPrice',
                    'price',
                    'stockStatus',
                    'vendorSku',
                    'increments',
                    'isRecommended',
                    'lastOrderedAt',
                    'lastOrderedQuantity',
                ],
            ],
            'isHazardous',
            'requiresPrescription',
            'requiresColdShipping',
            'isControlledSubstance',
            'requiresPedigree',
        ]);

    // Assert offers are not empty
    $this->assertNotEmpty($response->json('offers'));

});

it('returns product with offers only for connected vendors', function () {
    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}");

    $response->assertOk()
        ->assertJsonStructure([
            'id',
            'name',
            'imageUrl',
            'isFavorite',
            'manufacturer',
            'manufacturerSku',
            'description',
            'attributes',
            'offers' => [
                '*' => [
                    'id',
                    'vendor' => [
                        'id',
                        'name',
                        'imageUrl',
                    ],
                    'clinicPrice',
                    'price',
                    'stockStatus',
                    'vendorSku',
                    'increments',
                    'isRecommended',
                    'lastOrderedAt',
                    'lastOrderedQuantity',
                ],
            ],
            'isHazardous',
            'requiresPrescription',
            'requiresColdShipping',
            'isControlledSubstance',
            'requiresPedigree',
        ]);

    // Assert offers are not empty
    $this->assertNotEmpty($response->json('offers'));

    // Assert the additional offer is NOT returned because the vendor is not connected
    $this->assertTrue(collect($response->json('offers'))->pluck('id')->doesntContain($this->additionalOffer->id));
});
