<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Clinics\Products;

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class GetProductWithBuyXGetYPromotionTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Clinic $clinic;

    protected Product $product;

    protected ProductOffer $productOffer;

    protected Vendor $vendor;

    protected GpoAccount $gpoAccount;

    protected ClinicAccount $account;

    protected Promotion $promotion;

    protected function setUp(): void
    {
        parent::setUp();

        $this->vendor = Vendor::factory()->enabled()->create();
        $this->product = Product::factory()->create();
        $this->productOffer = ProductOffer::factory()->create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
        ]);

        $this->gpoAccount = GpoAccount::factory()->create();
        $this->account = ClinicAccount::factory()->for($this->gpoAccount, 'gpo')->create();

        /** @var User $this->user */
        $this->user = User::factory()->for($this->account, 'account')->create();
        $this->user->assignRole(ClinicAccountRole::Owner);

        $this->clinic = Clinic::factory()->for($this->account, 'account')->create();

        // Attach product offer to clinic
        $this->clinic->productOffers()->attach($this->productOffer);

        // Create integration connection
        IntegrationConnection::create([
            'vendor_id' => $this->vendor->id,
            'clinic_id' => $this->clinic->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        // Create a Buy X Get Y promotion
        $this->promotion = Promotion::create([
            'name' => 'Buy X Get Y Test Promotion',
            'type' => PromotionType::BuyXGetY,
            'description' => 'Test Buy X Get Y promotion',
            'vendor_id' => $this->vendor->id,
            'priority' => 1,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
            'status' => PromotionStatus::Active,
            'promotionable_type' => get_class($this->gpoAccount),
            'promotionable_id' => $this->gpoAccount->id,
        ]);

        // Attach product offer to promotion
        $this->promotion->productOffers()->attach($this->productOffer->id);

        // Create rule with condition and action
        $rule = $this->promotion->rules()->create(['priority' => 1]);
        $rule->conditions()->create([
            'type' => ConditionType::MinimumQuantity,
            'config' => [
                'quantity' => 2,
            ],
        ]);
        $rule->actions()->create([
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'quantity' => 2,
            ],
        ]);
    }

    public function test_product_with_buy_x_get_y_promotion_returns_promotion_data(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}");

        $response->assertOk();

        $data = $response->json();
        $this->assertArrayHasKey('promotions', $data);

        $promotions = $data['promotions'];
        $this->assertNotEmpty($promotions);

        $promotion = collect($promotions)
            ->firstWhere('type', 'buy_x_get_y');

        $this->assertNotNull($promotion);
        $this->assertEquals('Buy X Get Y Test Promotion', $promotion['name']);
        $this->assertEquals('buy_x_get_y', $promotion['type']);
        $this->assertEquals('Test Buy X Get Y promotion', $promotion['description']);
        $this->assertArrayHasKey('vendor', $promotion);
        $this->assertArrayHasKey('requirements', $promotion);
        $this->assertArrayHasKey('benefits', $promotion);
    }

    public function test_product_without_buy_x_get_y_promotion_returns_empty_promotions(): void
    {
        // Create a product without any promotion
        $productWithoutPromotion = Product::factory()->create();

        $response = $this->actingAs($this->user)
            ->getJson("/api/clinics/{$this->clinic->id}/products/{$productWithoutPromotion->id}");

        $response->assertOk();

        $data = $response->json();
        $this->assertArrayHasKey('promotions', $data);
        $this->assertEmpty($data['promotions']);
    }

    public function test_inactive_buy_x_get_y_promotion_is_not_returned(): void
    {
        $this->promotion->update(['status' => PromotionStatus::Inactive]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}");

        $response->assertOk();

        $data = $response->json();
        $this->assertArrayHasKey('promotions', $data);
        $this->assertEmpty($data['promotions']);
    }

    public function test_expired_buy_x_get_y_promotion_is_not_returned(): void
    {
        $this->promotion->update(['ended_at' => now()->subDay()]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}");

        $response->assertOk();

        $data = $response->json();
        $this->assertArrayHasKey('promotions', $data);
        $this->assertEmpty($data['promotions']);
    }
}
