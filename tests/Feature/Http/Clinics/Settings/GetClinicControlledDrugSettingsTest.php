<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ClinicSetting;
use App\Models\User;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Testing\TestResponse;

function getSettings(string $clinicId): TestResponse
{
    return test()->getJson("/api/clinics/{$clinicId}/settings/controlled-drugs");
}

function getValidCurrentSettingsValue(): array
{
    return [
        'controlledSubstancePurchases' => [
            'controlledSubstancePct' => 60.5,
            'nonControlledPrescriptionsPct' => 30.0,
            'nonPrescriptionsPct' => 9.5,
        ],
        'orderingPattern' => 'MONTHLY',
        'otherSuppliers' => 'Health Supply Co.',
        'topControlledSubstances' => [
            [
                'productName' => 'Oxycodone',
                'quantity' => 500,
            ],
            [
                'productName' => 'Morphine',
                'quantity' => 300,
            ],
        ],
        'intendedControlledSubstances' => [
            [
                'productName' => 'Hydrocodone',
                'strength' => '10mg',
                'quantity' => 200,
                'frequency' => 'WEEKLY',
            ],
        ],
        'isRegisteredWithCsos' => true,
        'lastDeaInspectionDate' => '2023-08-20',
        'administerMedicationsOnSite' => false,
        'takeBackControlledSubstances' => true,
        'reasonTakingBackControlledSubstances' => null,
        'areLicensesCurrent' => true,
        'registrantIssues' => [
            'Currently under investigation by DEA',
            'Ever been convicted of a related crime',
            'License revoked by DEA',
        ],
        'inventoryManager' => 'Jane Smith, Inventory Manager',
        'everCutOffFromPurchasing' => false,
        'maintainControlledSubstancesLog' => true,
        'reasonNotMaintainingLog' => null,
        'hasSecurityPolicies' => true,
        'reasonNoSecurityPolicies' => null,
        'areEmployeesTrained' => true,
        'reasonEmployeesNotTrained' => null,
        'hasOtherDeaBusinesses' => false,
        'reasonNotDeaPermit' => null,
    ];
}

beforeEach(function () {
    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
    $this->settings = ClinicSetting::factory()->ofControlledDrugs()->for($this->clinic)->create(
        ['value' => getValidCurrentSettingsValue()]
    );
});

it('prevents unauthorized users from accessing', function () {
    $clinic = Clinic::factory()->create();
    $response = getSettings($clinic->id);

    $response->assertUnauthorized();
});

it('makes a valid request and returns the correct response', function () {
    $this->actingAs($this->user);
    $response = getSettings($this->clinic->id);

    $response->assertOk()->assertJson(getValidCurrentSettingsValue());
});
