<?php

declare(strict_types=1);

use App\Enums\ClinicControlledDrugsOrderFrequency;
use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Testing\TestResponse;

function updateSettings(string $clinicId, array $data): TestResponse
{
    return test()->putJson("/api/clinics/{$clinicId}/settings/controlled-drugs", $data);
}

function getValidSettingsValue(): array
{
    return [
        'controlledSubstancePurchases' => [
            'controlledSubstancePct' => 60.5,
            'nonControlledPrescriptionsPct' => 30.0,
            'nonPrescriptionsPct' => 9.5,
        ],
        'orderingPattern' => 'MONTHLY',
        'otherSuppliers' => 'Health Supply Co.',
        'topControlledSubstances' => [
            [
                'productName' => 'Oxycodone',
                'quantity' => 500,
            ],
            [
                'productName' => 'Morphine',
                'quantity' => 300,
            ],
        ],
        'intendedControlledSubstances' => [
            [
                'productName' => 'Hydrocodone',
                'strength' => '10mg',
                'quantity' => 200,
                'frequency' => 'WEEKLY',
            ],
        ],
        'isRegisteredWithCsos' => true,
        'lastDeaInspectionDate' => '2023-08-20',
        'administerMedicationsOnSite' => false,
        'takeBackControlledSubstances' => true,
        'reasonTakingBackControlledSubstances' => 'Reason for taking back controlled substances',
        'areLicensesCurrent' => true,
        'registrantIssues' => [
            'Currently under investigation by DEA',
            'Ever been convicted of a related crime',
            'License revoked by DEA',
        ],
        'inventoryManager' => 'Jane Smith, Inventory Manager',
        'everCutOffFromPurchasing' => false,
        'maintainControlledSubstancesLog' => true,
        'reasonNotMaintainingLog' => null,
        'hasSecurityPolicies' => true,
        'reasonNoSecurityPolicies' => null,
        'areEmployeesTrained' => true,
        'reasonEmployeesNotTrained' => null,
        'hasOtherDeaBusinesses' => true,
        'reasonNotDeaPermit' => null,
    ];
}

beforeEach(function () {
    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
});

it('prevents unauthorized users from updating', function () {
    $clinic = Clinic::factory()->create();
    updateSettings($clinic->id, [])->assertUnauthorized();
});

it('makes a valid request and returns the correct response', function () {
    $this->actingAs($this->user);
    $data = getValidSettingsValue();
    $response = updateSettings($this->clinic->id, $data);

    $response->assertOk()->assertJson(getValidSettingsValue());
});

it('does not allow more than five item in top controlled substances', function () {
    $data = getValidSettingsValue();
    $data['top_controlled_substances'] = array_fill(0, 6, [
        'product_name' => 'Oxycodone',
        'quantity' => 500,
    ]);

    expect(count($data['top_controlled_substances']))->toEqual(6);

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);

    $response->assertUnprocessable();
});

it('does not allow more than five item in intended controlled substances', function () {
    $data = getValidSettingsValue();

    $data['intended_controlled_substances'] = array_fill(0, 6, [
        'product_name' => 'Hydrocodone',
        'strength' => '10mg',
        'quantity' => 200,
        'frequency' => ClinicControlledDrugsOrderFrequency::Weekly->value,
    ]);

    expect(count($data['intended_controlled_substances']))->toEqual(6);

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);
    $response->assertUnprocessable();
});

test('reason for not taking back controlled substances is required when taking back controlled substances is true', function () {
    $data = getValidSettingsValue();
    $data['take_back_controlled_substances'] = true;
    $data['reason_taking_back_controlled_substances'] = null;

    expect($data['reason_taking_back_controlled_substances'])->toBeFalsy();

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);
    $response->assertUnprocessable();
});

test('reason for not maintaining log is required when maintain controlled substances log is false', function () {
    $data = getValidSettingsValue();

    $data['maintain_controlled_substances_log'] = false;
    $data['reason_not_maintaining_log'] = null;

    expect($data['maintain_controlled_substances_log'])->toBeFalsy();
    expect($data['reason_not_maintaining_log'])->toBeFalsy();

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);
    $response->assertUnprocessable();
});

test('reason for no security policies is required when has security policies is false', function () {
    $data = getValidSettingsValue();

    $data['has_security_policies'] = false;
    $data['reason_no_security_policies'] = null;

    expect($data['has_security_policies'])->toBeFalsy();
    expect($data['reason_no_security_policies'])->toBeFalsy();

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);
    $response->assertUnprocessable();
});

test('reason for employees not trained is required when are employees trained is false', function () {
    $data = getValidSettingsValue();

    $data['are_employees_trained'] = false;
    $data['reason_employees_not_trained'] = null;

    expect($data['are_employees_trained'])->toBeFalsy();
    expect($data['reason_employees_not_trained'])->toBeFalsy();

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);
    $response->assertUnprocessable();
});

test('reason for no DEA permit is required when has other DEA businesses is false', function () {
    $data = getValidSettingsValue();

    $data['has_other_dea_businesses'] = false;
    $data['reason_not_dea_permit'] = null;

    expect($data['has_other_dea_businesses'])->toBeFalsy();
    expect($data['reason_not_dea_permit'])->toBeFalsy();

    $this->actingAs($this->user);
    $response = updateSettings($this->clinic->id, $data);
    $response->assertUnprocessable();
});
