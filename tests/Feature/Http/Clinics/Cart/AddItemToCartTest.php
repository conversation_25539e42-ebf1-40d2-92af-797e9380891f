<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('adds an item to the cart', function () {
    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/cart/cart-items", [
            'items' => [
                [
                    'product_offer_id' => $this->productOffer->id,
                    'quantity' => 1,
                ],
            ],
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'budget',
            'subtotal',
            'vendors',
        ]);

    $cart = $this->clinic->cart()->first();

    expect($cart)->not()->toBeNull();
    expect($cart->items()->count())->toBe(1);
});

it('adds an item with the correct price', function () {
    $this->clinic->productOffers()->attach($this->productOffer, [
        'price' => 100,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/cart/cart-items", [
            'items' => [
                [
                    'product_offer_id' => $this->productOffer->id,
                    'quantity' => 1,
                ],
            ],
        ]);

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'id' => $this->vendor->id,
                    'items' => [
                        [
                            'productOfferId' => $this->productOffer->id,
                            'price' => '1.00',
                        ],
                    ],
                ],
            ],
        ]);
});
