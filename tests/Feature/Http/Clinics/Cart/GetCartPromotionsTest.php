<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Carbon\Carbon;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create([
        'is_enabled' => true,
    ]);
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->gpoAccount = GpoAccount::factory()->create();
    $this->account = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
    $this->clinic->productOffers()->attach($this->productOffer);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('returns empty promotion data when cart is empty', function () {
    $this->clinic->cart()->create();

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJson([
            'eligiblePromotions' => [],
            'itemsWithPromotions' => [],
        ]);
});

it('returns empty promotion data when no promotions exist', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJson([
            'eligiblePromotions' => [],
            'itemsWithPromotions' => [],
        ]);
});

it('returns eligible promotions when cart qualifies', function () {
    $promotion = Promotion::factory()->create([
        'name' => 'Test BOGO Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => Carbon::yesterday(),
        'ended_at' => Carbon::tomorrow(),
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $promotion->productOffers()->attach($this->productOffer->id);

    $promotion->rules()->create([
        'priority' => 1,
    ]);

    $cart = $this->clinic->cart()->create();
    $cartItem = $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJsonStructure([
            'eligiblePromotions' => [
                '*' => [
                    'promotion' => [
                        'id',
                        'name',
                        'type',
                        'description',
                        'startedAt',
                        'endedAt',
                        'vendor',
                    ],
                    'applicableItems',
                    'missingRequirements',
                ],
            ],
            'itemsWithPromotions' => [
                '*' => [
                    '*' => [
                        'promotionId',
                        'promotionName',
                        'promotionType',
                        'isEligible',
                    ],
                ],
            ],
        ])
        ->assertJsonPath('eligiblePromotions.0.promotion.name', 'Test BOGO Promotion')
        ->assertJsonPath('eligiblePromotions.0.promotion.type', 'buy_x_get_y')
        ->assertJsonPath('eligiblePromotions.0.applicableItems.0', $cartItem->id)
        ->assertJsonPath('eligiblePromotions.0.missingRequirements', [])
        ->assertJsonStructure([
            'itemsWithPromotions' => [
                '*' => [
                    '*' => [
                        'promotionId',
                        'promotionName',
                        'promotionType',
                        'isEligible',
                    ],
                ],
            ],
        ]);
});

it('returns no eligible promotions when cart does not qualify', function () {
    $promotion = Promotion::factory()->create([
        'name' => 'Test Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => Carbon::yesterday(),
        'ended_at' => Carbon::tomorrow(),
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $promotion->productOffers()->attach($this->productOffer->id);

    $rule = $promotion->rules()->create([
        'priority' => 1,
    ]);

    $rule->conditions()->create([
        'type' => 'minimum_quantity',
        'config' => ['quantity' => 5],
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJson([
            'eligiblePromotions' => [],
            'itemsWithPromotions' => [],
        ]);
});

it('does not return expired promotions', function () {
    $promotion = Promotion::factory()->create([
        'name' => 'Expired Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => Carbon::parse('2023-01-01'),
        'ended_at' => Carbon::parse('2023-01-31'),
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $promotion->productOffers()->attach($this->productOffer->id);

    $promotion->rules()->create([
        'priority' => 1,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJson([
            'eligiblePromotions' => [],
            'itemsWithPromotions' => [],
        ]);
});

it('does not return inactive promotions', function () {
    $promotion = Promotion::factory()->create([
        'name' => 'Inactive Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Inactive,
        'started_at' => Carbon::yesterday(),
        'ended_at' => Carbon::tomorrow(),
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $promotion->productOffers()->attach($this->productOffer->id);

    $promotion->rules()->create([
        'priority' => 1,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJson([
            'eligiblePromotions' => [],
            'itemsWithPromotions' => [],
        ]);
});

it('handles promotions for different GPO accounts correctly', function () {
    $otherGpo = GpoAccount::factory()->create();
    $promotion = Promotion::factory()->create([
        'name' => 'Other GPO Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => Carbon::yesterday(),
        'ended_at' => Carbon::tomorrow(),
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $otherGpo->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $promotion->productOffers()->attach($this->productOffer->id);

    $promotion->rules()->create([
        'priority' => 1,
    ]);

    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->getJson("/api/clinics/{$this->clinic->id}/cart/promotions");

    $response->assertOk()
        ->assertJson([
            'eligiblePromotions' => [],
            'itemsWithPromotions' => [],
        ]);
});
