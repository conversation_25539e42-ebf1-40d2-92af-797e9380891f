<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
});

it('cleans the cart', function () {
    $cart = $this->clinic->cart()->create();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->deleteJson("/api/clinics/{$this->clinic->id}/cart");

    $response->assertOk()
        ->assertJson([
            'budget' => [
                'weekToDate' => [
                    [
                        'category' => 'COGS',
                        'spent' => '0.00',
                        'spentPercentage' => 0,
                        'target' => '0.00',
                        'targetPercentage' => 0,
                    ],
                    [
                        'category' => 'GA',
                        'spent' => '0.00',
                        'spentPercentage' => 0,
                        'target' => '0.00',
                        'targetPercentage' => 0,
                    ],
                ],
            ],
        ]);

    expect($cart->items()->count())->toBe(0);
});
