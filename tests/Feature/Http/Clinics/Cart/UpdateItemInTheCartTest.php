<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()
        ->create([
            'account_id' => $this->account->id,
        ]);
    $this->clinic = Clinic::factory()
        ->create([
            'clinic_account_id' => $this->account->id,
        ]);
    $this->clinic->productOffers()->attach($this->productOffer, ['price' => $this->productOffer->price]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('updates the quantity of an item in the cart', function () {
    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/clinics/{$this->clinic->id}/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 2,
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'budget' => [
                'type',
                'weekToDate' => [
                    '*' => [
                        'category',
                        'spent',
                        'spentPercentage',
                        'target',
                        'targetPercentage',
                    ],
                ],
                'monthToDate' => [
                    '*' => [
                        'category',
                        'spent',
                        'spentPercentage',
                        'target',
                        'targetPercentage',
                    ],
                ],
            ],
            'subtotal',
            'total',
            'itemsCount',
            'uniqueItemsCount',
            'vendors' => [
                '*' => [
                    'id',
                    'name',
                    'imageUrl',
                    'subtotal',
                    'total',
                    'shippingFee',
                    'amountToFreeShipping',
                    'cutoffTime',
                    'items' => [
                        '*' => [
                            'id',
                            'quantity',
                            'price',
                            'subtotal',
                            'notes',
                            'product' => [
                                'id',
                                'name',
                                'imageUrl',
                                'isFavorite',
                                'offers' => [
                                    '*' => [
                                        'id',
                                        'price',
                                        'vendor' => [
                                            'id',
                                            'name',
                                            'imageUrl',
                                        ],
                                        'lastOrderedAt',
                                        'lastOrderedQuantity',
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);

    expect($cart->items()->first()->quantity)->toBe(2);
});

it('removes an item from the cart when quantity is 0', function () {
    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/clinics/{$this->clinic->id}/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 0,
        ]);

    $response->assertJsonCount(0, 'vendors');

    expect($cart->items()->count())->toBe(0);
});

it('adds a note to the cart item', function () {
    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/clinics/{$this->clinic->id}/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $this->productOffer->id,
            'notes' => 'Do not open until Monday',
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'budget',
            'subtotal',
            'vendors',
        ]);

    expect($cart->items()->first()->notes)->toBe('Do not open until Monday');
});

it('swaps the product of the cart item', function () {
    $productB = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 990,
    ]);

    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 1090,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/clinics/{$this->clinic->id}/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $productB->id,
        ]);

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'items' => [
                        [
                            'price' => '9.90',
                            'quantity' => 2,
                            'subtotal' => '19.80',
                        ],
                    ],
                ],
            ],
        ]);

    expect($cart->items()->count())->toBe(1);
    expect($cart->items()->first()->price)->toBe(990);
});

it('swaps the product of the cart item using the correct price and quantity', function () {
    $productB = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 990,
    ]);

    $productB->clinics()->attach($this->clinic->id, ['price' => 999]);

    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 1090,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/clinics/{$this->clinic->id}/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $productB->id,
        ]);

    $response->assertOk()
        ->assertJson([
            'vendors' => [
                [
                    'items' => [
                        [
                            'price' => '9.99',
                            'quantity' => 2,
                            'subtotal' => '19.98',
                        ],
                    ],
                ],
            ],
        ]);

    expect($cart->items()->count())->toBe(1);
    expect($cart->items()->first()->price)->toBe(999);
});
