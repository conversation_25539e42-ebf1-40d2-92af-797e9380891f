<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Carbon\Carbon;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create([
        'is_enabled' => true,
    ]);
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->gpoAccount = GpoAccount::factory()->create();
    $this->account = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
    $this->clinic->productOffers()->attach($this->productOffer);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('adds item to cart and returns promotion data', function () {
    $promotion = Promotion::factory()->create([
        'name' => 'Test BOGO Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => Carbon::yesterday(),
        'ended_at' => Carbon::tomorrow(),
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $this->gpoAccount->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $promotion->productOffers()->attach($this->productOffer->id);
    $promotion->rules()->create([
        'priority' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/cart/cart-items", [
            'items' => [
                [
                    'product_offer_id' => $this->productOffer->id,
                    'quantity' => 2,
                ],
            ],
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'promotions' => [
                'eligiblePromotions',
                'itemsWithPromotions',
            ],
        ]);
});

it('adds item to cart and always includes promotion data', function () {
    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->postJson("/api/clinics/{$this->clinic->id}/cart/cart-items", [
            'items' => [
                [
                    'product_offer_id' => $this->productOffer->id,
                    'quantity' => 2,
                ],
            ],
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'promotions' => [
                'eligiblePromotions',
                'itemsWithPromotions',
            ],
            'vendors' => [
                '*' => [
                    'items' => [
                        '*' => [
                            'id',
                            'quantity',
                            'price',
                        ],
                    ],
                ],
            ],
        ]);
});
