<?php

declare(strict_types=1);

use App\Enums\AddressType;
use App\Enums\VendorType;
use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Clinic\Enums\ShoppingPreference;
use App\Modules\Integration\Enums\IntegrationConnectionRole;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Testing\TestResponse;

function updateClinic(Clinic $clinic, array $data): TestResponse
{

    return test()->patchJson("/api/clinics/{$clinic->id}", $data);
}

beforeEach(function () {
    $this->owner = User::factory()->create();
    $this->member = User::factory()->create();

    $this->account = ClinicAccount::factory()->create();
    $this->owner->update([
        'account_id' => $this->account->id,
    ]);
    $this->member->update([
        'account_id' => $this->account->id,
    ]);

    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
        'name' => 'Animal Talk Clinic',
        'phone_number' => '**************',
    ]);
    $this->clinic->billingAddress()->create([
        'type' => AddressType::Billing,
        'street' => '742 Evergreen Terrace',
        'city' => 'Anytown',
        'state' => 'CA',
        'postal_code' => '12345',
    ]);
    $this->clinic->shippingAddress()->create([
        'type' => AddressType::Shipping,
        'street' => '742 Evergreen Terrace',
        'city' => 'Anytown',
        'state' => 'CA',
        'postal_code' => '12345',
    ]);
});

describe('update clinic', function () {
    it('allows clinic owners to update the clinic', function () {
        $this->actingAs($this->owner);

        updateClinic($this->clinic, [
            'name' => 'Purrfect Pals Pet Clinic',
            'phoneNumber' => '1-888-MEOW-WOOF',
            'address' => [
                'street' => '123 Whisker Lane',
                'city' => 'Pawsville',
                'state' => 'NY',
                'postalCode' => '54321',
            ],
        ])
            ->assertOk()
            ->assertJson([
                'name' => 'Purrfect Pals Pet Clinic',
                'phoneNumber' => '1-888-MEOW-WOOF',
                'billingAddress' => [
                    'street' => '123 Whisker Lane',
                    'city' => 'Pawsville',
                    'state' => 'NY',
                    'postalCode' => '54321',
                ],
                'shippingAddress' => [
                    'street' => '123 Whisker Lane',
                    'city' => 'Pawsville',
                    'state' => 'NY',
                    'postalCode' => '54321',
                ],
            ]);

        $this->clinic->refresh();

        expect($this->clinic->name)->toBe('Purrfect Pals Pet Clinic');
        expect($this->clinic->phone_number)->toBe('1-888-MEOW-WOOF');
        expect($this->clinic->billingAddress)
            ->street->toBe('123 Whisker Lane')
            ->city->toBe('Pawsville')
            ->state->toBe('NY')
            ->postal_code->toBe('54321');
        expect($this->clinic->shippingAddress)
            ->street->toBe('123 Whisker Lane')
            ->city->toBe('Pawsville')
            ->state->toBe('NY')
            ->postal_code->toBe('54321');
    });

    it('does not allow non-owners to update the clinic', function () {
        $this->actingAs($this->member);

        updateClinic($this->clinic, [])->assertForbidden();

        $this->clinic->refresh();

        expect($this->clinic->name)->toBe('Animal Talk Clinic');
        expect($this->clinic->phone_number)->toBe('**************');
        expect($this->clinic->billingAddress)
            ->street->toBe('742 Evergreen Terrace')
            ->city->toBe('Anytown')
            ->state->toBe('CA')
            ->postal_code->toBe('12345');
        expect($this->clinic->shippingAddress)
            ->street->toBe('742 Evergreen Terrace')
            ->city->toBe('Anytown')
            ->state->toBe('CA')
            ->postal_code->toBe('12345');
    })->skip('TODO: implement user role checks');

    it('does not allow updating to a duplicate name', function () {
        Clinic::factory()->create(['name' => 'Existing Clinic']);

        $this->actingAs($this->owner);

        updateClinic($this->clinic, ['name' => 'Existing Clinic'])->assertUnprocessable();
    })->todo();

    it('does not clear the address when updating the clinic', function () {
        $this->actingAs($this->owner);

        updateClinic($this->clinic, ['address' => null])->assertUnprocessable();
    });

    it('does not clear empty fields when updating the clinic', function () {
        $this->actingAs($this->owner);

        updateClinic($this->clinic, [])->assertOk();

        $this->clinic->refresh();

        expect($this->clinic->name)->toBe('Animal Talk Clinic');
        expect($this->clinic->phone_number)->toBe('**************');
        expect($this->clinic->billingAddress)
            ->street->toBe('742 Evergreen Terrace')
            ->city->toBe('Anytown')
            ->state->toBe('CA')
            ->postal_code->toBe('12345');
        expect($this->clinic->shippingAddress)
            ->street->toBe('742 Evergreen Terrace')
            ->city->toBe('Anytown')
            ->state->toBe('CA')
            ->postal_code->toBe('12345');
    });

    it('updates primary distributor integration connection role field', function () {
        $vendor = Vendor::factory()->create([
            'type' => VendorType::Distributor,
        ]);

        $primaryDistributorIntegrationConnection = IntegrationConnection::create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $this->actingAs($this->owner);

        updateClinic($this->clinic, [
            'name' => 'Purrfect Pals Pet Clinic',
            'phoneNumber' => '1-888-MEOW-WOOF',
            'address' => [
                'street' => '123 Whisker Lane',
                'city' => 'Pawsville',
                'state' => 'NY',
                'postalCode' => '54321',
            ],
            'primary_distributor_id' => $primaryDistributorIntegrationConnection->vendor->id,
        ])
            ->assertOk();

        $this->clinic->refresh();

        $primaryDistributorIntegrationConnection->refresh();
        $this->assertEquals(
            IntegrationConnectionRole::PrimaryDistributor->value,
            $primaryDistributorIntegrationConnection->role
        );
    });

    it('updates secondary distributor integration connection role field', function () {
        $vendor = Vendor::factory()->create([
            'type' => VendorType::Distributor,
        ]);

        $secondaryDistributorIntegrationConnection = IntegrationConnection::create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $this->actingAs($this->owner);

        updateClinic($this->clinic, [
            'name' => 'Purrfect Pals Pet Clinic',
            'phoneNumber' => '1-888-MEOW-WOOF',
            'address' => [
                'street' => '123 Whisker Lane',
                'city' => 'Pawsville',
                'state' => 'NY',
                'postalCode' => '54321',
            ],
            'secondary_distributor_id' => $secondaryDistributorIntegrationConnection->vendor->id,
        ])
            ->assertOk();

        $this->clinic->refresh();

        $secondaryDistributorIntegrationConnection->refresh();
        $this->assertEquals(
            IntegrationConnectionRole::SecondaryDistributor->value,
            $secondaryDistributorIntegrationConnection->role
        );
    });

    it('updates preffered manufacturers integration connection role field', function () {
        $vendor = Vendor::factory()->create([
            'type' => VendorType::Manufacturer,
        ]);

        $preferredManufacturerIntegrationConnection = IntegrationConnection::create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $this->actingAs($this->owner);

        updateClinic($this->clinic, [
            'name' => 'Purrfect Pals Pet Clinic',
            'phoneNumber' => '1-888-MEOW-WOOF',
            'address' => [
                'street' => '123 Whisker Lane',
                'city' => 'Pawsville',
                'state' => 'NY',
                'postalCode' => '54321',
            ],
            'preferred_manufacturer_ids' => [
                $preferredManufacturerIntegrationConnection->vendor->id,
            ],
        ])
            ->assertOk();

        $this->clinic->refresh();

        $preferredManufacturerIntegrationConnection->refresh();
        $this->assertEquals(
            IntegrationConnectionRole::PreferredManufacturer->value,
            $preferredManufacturerIntegrationConnection->role
        );
    });

    it('cleans role field on integration connection', function () {
        $vendor = Vendor::factory()->create([
            'type' => VendorType::Distributor,
        ]);

        $secondaryDistributorIntegrationConnection = IntegrationConnection::create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $this->actingAs($this->owner);

        updateClinic($this->clinic, [
            'name' => 'Purrfect Pals Pet Clinic',
            'phoneNumber' => '1-888-MEOW-WOOF',
            'address' => [
                'street' => '123 Whisker Lane',
                'city' => 'Pawsville',
                'state' => 'NY',
                'postalCode' => '54321',
            ],
            'primary_distributor_id' => null,
            'secondary_distributor_id' => null,
            'preferred_manufacturer_ids' => [],
        ])
            ->assertOk();

        $this->clinic->refresh();

        $secondaryDistributorIntegrationConnection->refresh();
        $this->assertEquals(
            null,
            $secondaryDistributorIntegrationConnection->role
        );
    });

    it('updates new enums', function () {
        $vendor = Vendor::factory()->create([
            'type' => VendorType::Manufacturer,
        ]);

        $this->actingAs($this->owner);

        updateClinic($this->clinic, [
            'name' => 'Purrfect Pals Pet Clinic',
            'phoneNumber' => '1-888-MEOW-WOOF',
            'address' => [
                'street' => '123 Whisker Lane',
                'city' => 'Pawsville',
                'state' => 'NY',
                'postalCode' => '54321',
            ],
            'primary_shopping_preference' => ShoppingPreference::BrandLoyalty->value,
            'secondary_shopping_preferences' => [
                ShoppingPreference::DistributorLoyalty->value,
                ShoppingPreference::GpoProductsPreferred->value,
            ],
        ])
            ->assertOk();

        $this->clinic->refresh();

        $this->assertEquals($this->clinic->primary_shopping_preference->pluck('value')->toArray(), [ShoppingPreference::BrandLoyalty->value]);
        $this->assertEquals($this->clinic->secondary_shopping_preferences->pluck('value')->toArray(), [
            ShoppingPreference::DistributorLoyalty->value,
            ShoppingPreference::GpoProductsPreferred->value,
        ]);
    });

});
