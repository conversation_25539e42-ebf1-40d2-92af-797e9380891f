<?php

declare(strict_types=1);

use App\Enums\AddressType;
use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Testing\TestResponse;

function getClinic(string $clinicId): TestResponse
{
    return test()->getJson("/api/clinics/{$clinicId}");
}

beforeEach(function () {
    $this->account = ClinicAccount::factory()->create();

    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
        'name' => 'Pawsome Pet Hospital',
        'business_tax_id' => '*********',
        'phone_number' => '(*************',
    ]);
    $this->clinic->billingAddress()->create([
        'type' => AddressType::Billing,
        'street' => '742 Whisker Lane',
        'city' => 'Furrington',
        'state' => 'CA',
        'postal_code' => '90210',
    ]);
    $this->clinic->shippingAddress()->create([
        'type' => AddressType::Shipping,
        'street' => '742 Whisker Lane',
        'city' => 'Furrington',
        'state' => 'CA',
        'postal_code' => '90210',
    ]);
});

test('unauthenticated users cannot view clinics', function () {
    getClinic($this->clinic->id)->assertUnauthorized();
});

test('account users can view clinics', function () {
    $user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);

    $this->actingAs($user);

    getClinic($this->clinic->id)->assertOk();
});

test('the api response structure is correct', function () {
    $user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);

    $this->actingAs($user);

    $response = getClinic($this->clinic->id);

    // expect($response)->toMatchSnapshot();
});
