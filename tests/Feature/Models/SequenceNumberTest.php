<?php

declare(strict_types=1);

use App\Support\SequenceNumber;

it('starts with 1000 for a new sequence', function () {
    expect(SequenceNumber::next('TEST'))->toBe('TEST000001');
});

it('increments the sequencial number for subsequent calls', function () {
    expect(SequenceNumber::next('TEST'))->toBe('TEST000001');
    expect(SequenceNumber::next('TEST'))->toBe('TEST000002');

    expect(SequenceNumber::next('OTHER'))->toBe('OTHER000001');
    expect(SequenceNumber::next('OTHER'))->toBe('OTHER000002');
});
