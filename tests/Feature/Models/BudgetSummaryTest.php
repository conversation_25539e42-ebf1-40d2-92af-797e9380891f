<?php

declare(strict_types=1);

use App\Enums\ClinicBudgetType;
use App\Enums\ExpenseCategory;
use App\Enums\TimePeriod;
use App\Models\BudgetMetric;
use App\Models\BudgetSummary;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
});

it('calculates static budget metrics', function () {
    $this->clinic->budgetSettings()->create([
        'type' => ClinicBudgetType::Static,
        'weekly_cogs' => 10000,
        'monthly_cogs' => 40000,
        'weekly_ga' => 2000,
        'monthly_ga' => 8000,
    ]);

    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 0, 0.0, 10000, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 0, 0.0, 40000, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::GA, TimePeriod::WeekToDate, 0, 0.0, 2000, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::GA, TimePeriod::MonthToDate, 0, 0.0, 8000, 0.0));
});

it('calculates dynamic budget metrics', function () {
    $this->clinic->budgetSettings()->create([
        'type' => ClinicBudgetType::Dynamic,
        'target_cogs_percent' => 0.5,
        'target_ga_percent' => 0.25,
        'avg_two_weeks_sales' => 10000,
        'month_to_date_sales' => 40000,
    ]);

    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 0, 0.0, 5000, 50.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 0, 0.0, 20000, 50.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::GA, TimePeriod::WeekToDate, 0, 0.0, 2500, 25.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::GA, TimePeriod::MonthToDate, 0, 0.0, 10000, 25.0));
});

it('provides zero metrics when budget settings are absent', function () {
    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 0, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 0, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::GA, TimePeriod::WeekToDate, 0, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::GA, TimePeriod::MonthToDate, 0, 0.0, 0, 0.0));
});

it('includes external data in metrics', function () {
    $this->clinic->budgetSettings()->create([
        'type' => ClinicBudgetType::Static,
        'include_external_data' => true,
        'external_weekly_cogs' => 1000,
        'external_monthly_cogs' => 4000,
    ]);

    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 1000, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 4000, 0.0, 0, 0.0));
});

it('includes cart items in metrics', function () {
    $this->vendor->update(['expense_category' => ExpenseCategory::COGS]);

    $this->clinic
        ->cart()
        ->create()
        ->items()
        ->create([
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 2,
            'price' => 1000,
        ]);

    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 2000, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 2000, 0.0, 0, 0.0));
});

it('includes clinic orders in metrics', function () {
    $this->vendor->update(['expense_category' => ExpenseCategory::COGS]);

    $order = Order::factory()->for($this->clinic)->create();

    OrderItem::factory()
        ->for($order)
        ->for($this->productOffer)
        ->create([
            'quantity' => 2,
            'price' => 1000,
        ]);

    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 2000, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 2000, 0.0, 0, 0.0));
});

it('respects order dates when calculating metrics', function () {
    $this->travelTo(Carbon::parse('2024-11-25'));

    $this->vendor->update(['expense_category' => ExpenseCategory::COGS]);

    $order = Order::factory()->for($this->clinic)->create(['created_at' => now()->subWeeks(2)]);

    OrderItem::factory()
        ->for($order)
        ->for($this->productOffer)
        ->create([
            'quantity' => 2,
            'price' => 1000,
        ]);

    $budget = new BudgetSummary($this->clinic);

    expect($budget)
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::WeekToDate, 0, 0.0, 0, 0.0))
        ->metrics->toContainEqual(new BudgetMetric(ExpenseCategory::COGS, TimePeriod::MonthToDate, 2000, 0.0, 0, 0.0));
});
