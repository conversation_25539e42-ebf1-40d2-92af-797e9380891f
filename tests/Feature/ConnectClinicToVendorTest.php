<?php

declare(strict_types=1);

use App\Enums\ProductSyncStatus;
use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Events\ConnectingIntegration;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Services\Vendors\Contracts\Service;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Testing\TestResponse;

use function Pest\Laravel\assertDatabaseHas;

function connectClinicToVendor(string $clinicId, array $data): TestResponse
{
    return test()->withHeaders([
        'highfive-clinic' => $clinicId,
    ])->postJson("/api/clinics/{$clinicId}/vendors", $data);

}

function getValidConnectionData(array $overrides = []): array
{
    return array_merge([
        'vendor_id' => null,
        'credentials' => [
            'username' => '<EMAIL>',
            'password' => 'gallifreyan-secret',
        ],
    ], $overrides);
}

beforeEach(function () {
    $this->doctor = User::factory()->create(['name' => 'The Doctor']);
    $this->companion = User::factory()->create(['name' => 'Rose Tyler']);
    $this->vendor = Vendor::factory()->enabled()->create(['name' => 'Sonic Screwdriver Inc.', 'integration_points' => [IntegrationPoint::SyncProductCatalog]]);
    $this->clinic = Clinic::factory()->create(['name' => 'TARDIS Veterinary Clinic']);

    $this->doctor->update(['account_id' => $this->clinic->account->id]);
    $this->companion->update(['account_id' => $this->clinic->account->id]);

    $fetchUrl = config('highfive.catalog_sync.service_endpoint');
    Http::fake([
        "$fetchUrl/api/scrape" => Http::response([], 200),
    ]);
    Event::fake();
});

describe('connect clinic to vendor', function () {
    describe('successful connection', function () {
        it('connects a clinic to a vendor', function () {
            $this->actingAs($this->doctor);

            $data = getValidConnectionData(['vendor_id' => $this->vendor->id]);

            $mock = $this->getMockBuilder(Service::class)->getMock();

            $this->app->bind(Service::class, fn () => $mock);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED)
                ->assertJson([
                    [
                        'id' => $this->vendor->id,
                        'status' => IntegrationConnectionStatus::Connected->value,
                    ],
                ]);

            expect($this->clinic->vendors()->count())->toBe(1);
        })->skip();

        it('returns the vendor connection status and integration points', function () {
            $this->actingAs($this->doctor);
            Queue::fake();
            Event::fake();
            $data = getValidConnectionData(['vendor_id' => $this->vendor->id,
                'credentials' => [
                    'username' => '<EMAIL>',
                    'password' => 'moonwalk', ]]);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED)
                ->assertJson([
                    [
                        'id' => $this->vendor->id,
                        'status' => IntegrationConnectionStatus::Connecting->value,
                        'integrationPoints' => [
                            IntegrationPoint::SyncProductCatalog->value,
                        ],
                    ],
                ]);

            expect($this->clinic->vendors()->count())->toBe(1);
        });

        it('stores the credentials encrypted', function () {
            $this->actingAs($this->doctor);

            $data = getValidConnectionData([
                'vendor_id' => $this->vendor->id,
                'credentials' => [
                    'username' => '<EMAIL>',
                    'password' => 'time-lord-supreme',
                ],
            ]);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED);

            $connection = IntegrationConnection::where('vendor_id', $this->vendor->id)
                ->where('clinic_id', $this->clinic->id)
                ->first();

            expect($connection->credentials)->toBe($data['credentials']);

            $raw = $connection->getAttributes()['credentials'];
            expect($raw)->not->toBe(json_encode($data['credentials']));

            Event::assertDispatched(ConnectingIntegration::class);
        });

        it('excludes credentials from the response', function () {
            $this->actingAs($this->doctor);

            $data = getValidConnectionData(['vendor_id' => $this->vendor->id]);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED)
                ->assertJsonMissing(['credentials']);

            Event::assertDispatched(ConnectingIntegration::class);
        });

        it('starts the products synchronization', function () {
            Queue::fake();

            $this->actingAs($this->doctor);

            $data = getValidConnectionData(['vendor_id' => $this->vendor->id]);

            expect($this->clinic->productsSyncs()->count())->toBe(0);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED);

            expect($this->clinic->productsSyncs()->count())->toBe(1);
            expect($this->clinic->productsSyncs()->first()->status)->toBe(ProductSyncStatus::Pending);
        })->skip();

        it('marks the connection as disconnected if the customer information cannot be retrieved', function () {
            $this->actingAs($this->doctor);

            $data = getValidConnectionData(['vendor_id' => $this->vendor->id]);

            $mock = $this->getMockBuilder(Service::class)->getMock();

            $this->app->bind(Service::class, fn () => $mock);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED)
                ->assertJson([
                    [
                        'id' => $this->vendor->id,
                        'status' => IntegrationConnectionStatus::Disconnected->value,
                    ],
                ]);

            expect($this->clinic->vendors()->count())->toBe(1);
            expect($this->clinic->vendors()->first()->pivot)
                ->error_message->toBe('Failed to retrieve customer information');
        })->skip();

        it('marks the connection as connected if there is no vendor service implementation', function () {
            $this->actingAs($this->doctor)->withHeader('highfive-clinic', $this->clinic->id);

            $vendor = Vendor::factory()->enabled()->create(['name' => 'Testing 2.']);
            $data = getValidConnectionData(['vendor_id' => $vendor->id]);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertStatus(Response::HTTP_CREATED)
                ->assertJsonPath('1.id', $vendor->id)
                ->assertJsonPath('1.status', IntegrationConnectionStatus::Connected->value);

            expect($this->clinic->vendors()->count())->toBe(1);
            assertDatabaseHas(
                'integration_connections',
                [
                    'vendor_id' => $vendor->id,
                    'clinic_id' => $this->clinic->id,
                    'status' => IntegrationConnectionStatus::Connected->value,
                    'error_message' => null,
                ]
            );

            Event::assertDispatched(ConnectingIntegration::class);
        });
    });

    describe('validation', function () {
        it('can only connect to enabled vendors', function () {
            $this->actingAs($this->doctor);

            $disabledVendor = Vendor::factory()->create(['is_enabled' => false]);

            $data = getValidConnectionData(['vendor_id' => $disabledVendor->id]);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertUnprocessable()
                ->assertJsonValidationErrors('vendorId');
        })->skip('TODO: implement user role checks');
    });

    describe('authorization', function () {
        it('prevents unauthorized users from connecting to vendors', function () {
            $this->actingAs($this->companion);

            $data = getValidConnectionData(['vendorId' => $this->vendor->id]);

            connectClinicToVendor($this->clinic->id, $data)
                ->assertForbidden();
        })->skip('TODO: implement user role checks');
    });
});
