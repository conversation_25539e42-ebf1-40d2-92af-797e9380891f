<?php

declare(strict_types=1);

use App\Enums\AddressType;
use App\Enums\ClinicOnboardingStatus;
use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Notifications\ManagerAddedToClinic;
use Illuminate\Support\Facades\Notification;
use Illuminate\Testing\TestResponse;

function createClinic(array $data): TestResponse
{
    return test()->postJson('/api/clinics', $data);
}

function getValidClinicData(array $overrides = []): array
{
    return array_merge([
        'name' => 'Pawnee Animal Hospital',
        'businessTaxId' => '12-3456789',
        'phoneNumber' => '************',
        'address' => [
            'street' => '123 Leslie Knope Ave',
            'city' => 'Pawnee',
            'state' => 'IN',
            'postalCode' => '46001',
        ],
    ], $overrides);
}

beforeEach(function () {
    $this->account = ClinicAccount::factory()->create();

    $this->owner = User::factory()->create([
        'account_id' => $this->account->id,
        'email' => '<EMAIL>',
    ]);
    $this->owner->assignRole(ClinicAccountRole::Owner);
    $this->member = User::factory()->create([
        'account_id' => $this->account->id,
        'email' => '<EMAIL>',
    ]);
});

describe('create clinic', function () {
    describe('create clinic successfully', function () {
        it('creates a single clinic', function () {
            $this->actingAs($this->owner);

            $data = getValidClinicData();

            createClinic($data)
                ->assertCreated()
                ->assertJson([
                    'name' => 'Pawnee Animal Hospital',
                    'businessTaxId' => '12-3456789',
                    'phoneNumber' => '************',
                ]);
        });

        it('creates multiple clinics at once', function () {
            $this->actingAs($this->owner);

            $downtown = getValidClinicData(['name' => 'Downtown Animal Hospital']);
            $westside = getValidClinicData(['name' => 'Westside Veterinary Clinic']);

            createClinic($this->account->id, ['clinics' => [$downtown, $westside]])
                ->assertCreated()
                ->assertJson([
                    ['name' => 'Downtown Animal Hospital', 'onboardingStatus' => ClinicOnboardingStatus::Done->value],
                    ['name' => 'Westside Veterinary Clinic', 'onboardingStatus' => ClinicOnboardingStatus::Done->value],
                ]);
        })->skip('TODO: multiple clinics refactor');
    });

    describe('authorization', function () {
        it('allows an account owner to create a clinic', function () {
            $this->actingAs($this->owner);

            $data = getValidClinicData();

            createClinic($data)
                ->assertCreated()
                ->assertJson([
                    'name' => 'Pawnee Animal Hospital',
                    'businessTaxId' => '12-3456789',
                    'phoneNumber' => '************',
                ]);
        });

        it('prevents an account member from creating a clinic', function () {
            $this->actingAs($this->member);

            $data = getValidClinicData();

            createClinic($data)
                ->assertForbidden();
        });
    });

    describe('validation', function () {
        it('validates EIN format', function () {
            $this->actingAs($this->owner);

            $data = getValidClinicData(['businessTaxId' => '*********']);

            createClinic($data)
                ->assertUnprocessable()
                ->assertJsonFragment([
                    'businessTaxId' => ['The business tax id must be in the format of 12-3456789.'],
                ]);
        });

        it('validates postal code format', function () {
            $this->actingAs($this->owner);

            $data = getValidClinicData(['address' => ['postalCode' => '*********']]);

            createClinic($data)
                ->assertUnprocessable()
                ->assertJsonFragment([
                    'address.postalCode' => ['The address.postal code must be in the format of 12345 or 12345-1234.'],
                ]);
        });

        it('cannot create a clinic with name only', function () {
            $this->actingAs($this->owner);

            $data = ['name' => 'Pawnee Animal Hospital'];

            createClinic($data)
                ->assertUnprocessable()
                ->assertJsonValidationErrors([
                    'address.street',
                    'address.city',
                    'address.state',
                    'address.postalCode',
                    'businessTaxId',
                    'phoneNumber',
                ]);
        });
    });

    describe('create address', function () {
        it('creates a billing address for the clinic', function () {
            $this->actingAs($this->owner);

            $data = getValidClinicData();

            $response = createClinic($data);

            $response->assertCreated();

            $this->assertDatabaseHas('addresses', [
                'addressable_id' => $response->json('id'),
                'addressable_type' => Clinic::class,
                'type' => AddressType::Billing,
                'street' => $data['address']['street'],
                'city' => $data['address']['city'],
                'state' => $data['address']['state'],
                'postal_code' => $data['address']['postalCode'],
            ]);
        });
    });

    describe('invite manager flow', function () {
        it('creates a manager and assigns them to the clinic', function () {
            $this->actingAs($this->owner);

            $data = getValidClinicData([
                'manager' => [
                    'name' => 'David Thompson',
                    'email' => '<EMAIL>',
                ],
            ]);

            createClinic($data)
                ->assertCreated();

            expect(User::where('email', '<EMAIL>')->exists())->toBeTrue();
        });

        it('sends a welcome email to the manager', function () {
            Notification::fake();

            $this->actingAs($this->owner);

            $data = getValidClinicData([
                'manager' => [
                    'name' => 'Emma Parker',
                    'email' => '<EMAIL>',
                ],
            ]);

            createClinic($data);

            Notification::assertSentTo(
                User::where('email', '<EMAIL>')->first(),
                ManagerAddedToClinic::class,
                function (ManagerAddedToClinic $notification) {
                    $this->patchJson('/api/users/me/password', [
                        'token' => $notification->token,
                        'email' => '<EMAIL>',
                        'password' => 'new-password',
                    ])
                        ->assertOk();

                    return true;
                }
            );
        });
    })->skip('TODO: invite manager flow refactor');
});
