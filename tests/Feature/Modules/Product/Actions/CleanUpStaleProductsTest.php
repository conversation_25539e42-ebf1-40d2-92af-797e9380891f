<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\Product\Actions\CleanUpStaleProducts;
use Illuminate\Support\Carbon;
use Spatie\SlackAlerts\Facades\SlackAlert;

describe('clean up stale products', function () {
    it('deactivates products that have not been synced in the last 7 days', function () {
        ProductOffer::factory()->create([
            'last_synced_at' => Carbon::now()->subDays(8),
        ]);

        expect(ProductOffer::query()->active()->count())->toBe(1);

        $action = new CleanUpStaleProducts();
        $deactivatedCount = $action->handle(7);

        expect($deactivatedCount)->toBe(1);
        expect(ProductOffer::query()->active()->count())->toBe(0);
    });

    it('removes the products from the clinics', function () {
        $product = ProductOffer::factory()->create([
            'last_synced_at' => Carbon::now()->subDays(8),
        ]);

        $clinic = Clinic::factory()->create();
        $clinic->productOffers()->attach($product->id);

        expect($clinic->productOffers()->count())->toBe(1);

        $action = new CleanUpStaleProducts();
        $action->handle(7);

        expect($clinic->productOffers()->count())->toBe(0);
    });

    it('does not deactivate products that have been synced in the last 7 days', function () {
        ProductOffer::factory()->create([
            'last_synced_at' => Carbon::now()->subDays(6),
        ]);

        expect(ProductOffer::query()->active()->count())->toBe(1);

        $action = new CleanUpStaleProducts();
        $deactivatedCount = $action->handle(7);

        expect($deactivatedCount)->toBe(0);
        expect(ProductOffer::query()->active()->count())->toBe(1);
    });

    it('does not deactivate products that have never been synced', function () {
        ProductOffer::factory()->create([
            'last_synced_at' => null,
        ]);

        expect(ProductOffer::query()->active()->count())->toBe(1);

        $action = new CleanUpStaleProducts();
        $deactivatedCount = $action->handle(7);

        expect($deactivatedCount)->toBe(0);
        expect(ProductOffer::query()->active()->count())->toBe(1);
    });

    it('does not deactivate products that are not active', function () {
        ProductOffer::factory()->create([
            'deactivated_at' => now(),
            'last_synced_at' => Carbon::now()->subDays(8),
        ]);

        expect(ProductOffer::query()->active()->count())->toBe(0);

        $action = new CleanUpStaleProducts();
        $deactivatedCount = $action->handle(7);

        expect($deactivatedCount)->toBe(0);
        expect(ProductOffer::query()->active()->count())->toBe(0);
    });

    it('does not deactivate products if the count is greater than the alert threshold', function () {
        ProductOffer::factory()->count(10)->create([
            'last_synced_at' => Carbon::now()->subDays(8),
        ]);

        SlackAlert::shouldReceive('message')->once();

        $alertThreshold = 5;
        $action = new CleanUpStaleProducts($alertThreshold);

        $deactivatedCount = $action->handle(7);

        expect($deactivatedCount)->toBe(0);
        expect(ProductOffer::query()->active()->count())->toBe(10);
    });

    it('deactivates products if the count is greater than the alert threshold and the force flag is set', function () {
        ProductOffer::factory()->count(10)->create([
            'last_synced_at' => Carbon::now()->subDays(8),
        ]);

        $action = new CleanUpStaleProducts(5);
        $deactivatedCount = $action->handle(7, force: true);

        expect($deactivatedCount)->toBe(10);
        expect(ProductOffer::query()->active()->count())->toBe(0);
    });
});
