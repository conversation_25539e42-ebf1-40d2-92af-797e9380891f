<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Support\Carbon;

it('get the purchase history for a product', function () {
    $product = ProductOffer::factory()->create();
    $account = ClinicAccount::create();
    $user = User::factory()->create([
        'account_id' => $account->id,
    ]);
    $clinic = Clinic::factory()->create([
        'clinic_account_id' => $account->id,
    ]);
    $clinic->productOffers()->attach($product->id);

    $order = Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'quantity' => 5,
                    'price' => 2300,
                ]),
            'items'
        )
        ->create();

    $this->actingAs($user);

    $this->getJson("/api/clinics/{$clinic->id}/product-offers/{$product->id}/purchase-history")
        ->assertOk()
        ->assertJsonCount(12, 'data')
        ->assertJson([
            'productOfferId' => $product->id,
            'summary' => [
                'totalSpent' => '115.00',
                'totalOrders' => 1,
                'averageQuantityPerOrder' => 5,
            ],
        ])
        ->assertJsonFragment([
            'label' => $order->created_at->format('Y-m'),
            'totalSpent' => '115.00',
            'totalOrders' => 1,
            'totalQuantity' => 5,
            'averageQuantityPerOrder' => 5,
        ]);
})->skip('Temporal Issue');

it('only returns purchase history for the current clinic', function () {
    $product = ProductOffer::factory()->create();
    $account = ClinicAccount::create();
    $user = User::factory()->create([
        'account_id' => $account->id,
    ]);
    $clinic = Clinic::factory()->create([
        'clinic_account_id' => $account->id,
    ]);
    $clinic->productOffers()->attach($product->id);

    /**
     * This order item is for a different clinic and shouldn't count towards the purchase history
     * of the product in the current clinic.
     */
    OrderItem::factory()
        ->for($product)
        ->create([
            'quantity' => 5,
            'price' => 2300,
        ]);

    $this->actingAs($user);

    $this->getJson("/api/clinics/{$clinic->id}/product-offers/{$product->id}/purchase-history")
        ->assertOk()
        ->assertJson([
            'productOfferId' => $product->id,
            'summary' => [
                'totalSpent' => '0.00',
                'totalOrders' => 0,
                'averageQuantityPerOrder' => 0,
            ],
        ]);
});

it('returns empty data when no purchase history exists', function () {
    $product = ProductOffer::factory()->create();
    $account = ClinicAccount::create();
    $user = User::factory()->create([
        'account_id' => $account->id,
    ]);
    $clinic = Clinic::factory()->create([
        'clinic_account_id' => $account->id,
    ]);
    $clinic->productOffers()->attach($product->id);

    $this->actingAs($user);

    $this->getJson("/api/clinics/{$clinic->id}/product-offers/{$product->id}/purchase-history")
        ->assertOk()
        ->assertJsonCount(12, 'data')
        ->assertJson([
            'productOfferId' => $product->id,
            'summary' => [
                'totalSpent' => '0.00',
                'lowestUnitPrice' => '0.00',
                'highestUnitPrice' => '0.00',
                'totalOrders' => 0,
                'averageQuantityPerOrder' => 0,
            ],
        ]);
})->skip('Temporal Issue');

it('rounds up the average quantity per order', function () {
    $product = ProductOffer::factory()->create();
    $account = ClinicAccount::create();
    $user = User::factory()->create([
        'account_id' => $account->id,
    ]);

    $clinic = Clinic::factory()->create([
        'clinic_account_id' => $account->id,
    ]);
    $clinic->productOffers()->attach($product->id);

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'quantity' => 3,
                    'price' => 2300,
                ]),
            'items'
        )
        ->create();

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'quantity' => 6,
                    'price' => 2300,
                ]),
            'items'
        )
        ->create();

    $this->actingAs($user);

    $this->getJson("/api/clinics/{$clinic->id}/product-offers/{$product->id}/purchase-history")
        ->assertOk()
        ->assertJson([
            'summary' => [
                'totalSpent' => '207.00',
                'totalOrders' => 2,
                'averageQuantityPerOrder' => 5,
            ],
        ]);
});

it('returns the lowest and highest price', function () {
    $product = ProductOffer::factory()->create();

    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->create(['account_id' => $account->id]);
    $user->assignRole(ClinicAccountRole::Owner);

    $clinic = Clinic::factory()->create(['clinic_account_id' => $account->id]);

    $clinic->productOffers()->attach($product->id);

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'created_at' => Carbon::now()->subMonth(5),
                    'quantity' => 1,
                    'price' => 1000,
                ]),
            'items'
        )
        ->create();

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'quantity' => 1,
                    'price' => 2000,
                ]),
            'items'
        )
        ->create();

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'created_at' => Carbon::now()->subMonth(1),
                    'quantity' => 1,
                    'price' => 3000,
                ]),
            'items'
        )
        ->create();

    $this->actingAs($user);

    $this->getJson("/api/clinics/{$clinic->id}/product-offers/{$product->id}/purchase-history")
        ->assertOk()
        ->assertJson([
            'summary' => [
                'totalSpent' => '60.00',
                'lowestUnitPrice' => '10.00',
                'highestUnitPrice' => '30.00',
            ],
        ]);
});

it('returns purchase history for current month and up to 11 prior months if no filters applied', function () {
    $product = ProductOffer::factory()->create();

    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->create(['account_id' => $account->id]);
    $user->assignRole(ClinicAccountRole::Owner);

    $clinic = Clinic::factory()->create(['clinic_account_id' => $account->id]);

    $clinic->productOffers()->attach($product->id);

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'created_at' => Carbon::now()->subMonth(12),
                    'quantity' => 1,
                    'price' => 1000,
                ]),
            'items'
        )
        ->create();

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'created_at' => Carbon::now()->subMonth(11),
                    'quantity' => 1,
                    'price' => 2000,
                ]),
            'items'
        )
        ->create();

    Order::factory()
        ->for($clinic)
        ->has(
            OrderItem::factory()
                ->for($product)
                ->state([
                    'created_at' => Carbon::now()->subMonth(1),
                    'quantity' => 1,
                    'price' => 3000,
                ]),
            'items'
        )
        ->create();

    $this->actingAs($user);

    $this->getJson("/api/clinics/{$clinic->id}/product-offers/{$product->id}/purchase-history")
        ->assertOk()
        ->assertJsonCount(12, 'data')
        ->assertJson([
            'summary' => [
                'totalSpent' => '50.00',
                'lowestUnitPrice' => '20.00',
                'highestUnitPrice' => '30.00',
            ],
        ]);
})->skip('Temporal Issue');
