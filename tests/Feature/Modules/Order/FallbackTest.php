<?php

declare(strict_types=1);

use App\Enums\AddressType;
use App\Mail\OrderPlacedForVendor;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\SubOrder;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Services\Vendor\Fallback;
use Database\Factories\AddressFactory;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;

test('fallback processOrder sends order email and logs success', function () {
    // Arrange
    Mail::fake();
    $bcc = '<EMAIL>';
    Config::set('highfive.orders.bcc_email', $bcc);

    // Create a vendor
    $vendor = Vendor::factory()->create();
    // Create a clinic
    $clinic = Clinic::factory()->create();
    // Add a shipping address to the clinic
    $clinic->shippingAddress()->create(AddressFactory::new(['type' => AddressType::Shipping])->make()->toArray());
    // Create an order for the clinic
    $order = Order::factory()->for($clinic)->create();
    // Create a product offer for the vendor
    $productOffer = ProductOffer::factory()->for($vendor)->create();
    // Create an order item for the order and product offer
    OrderItem::factory()->for($order)->for($productOffer)->create();
    // Create a suborder for the order and vendor
    $subOrder = SubOrder::factory()->for($order)->for($vendor)->create();
    // Create an integration connection for the vendor and clinic
    $integrationConnection = IntegrationConnection::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    $fallback = new Fallback();

    // Act
    $fallback->processOrder($subOrder, $integrationConnection);

    // Assert
    Mail::assertQueued(OrderPlacedForVendor::class, function ($mail) use ($subOrder) {
        return $mail->suborder->is($subOrder);
    });

    Mail::assertQueued(OrderPlacedForVendor::class, function ($mail) use ($subOrder) {
        return $mail->hasTo($subOrder->vendor->purchase_order_email);
    });

    // Check bcc
    Mail::assertQueued(OrderPlacedForVendor::class, function ($mail) use ($bcc) {
        return $mail->hasBcc($bcc);
    });

    // Check session log (success)
    $session = $integrationConnection->sessions()->latest()->first();
    expect($session)->not->toBeNull();
    expect($session->status)->toBe(IntegrationSessionStatus::Succeeded->value);
    expect($session->events()->where('action', 'fallback_order_email_sent')->exists())->toBeTrue();
});
