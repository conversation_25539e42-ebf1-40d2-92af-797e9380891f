<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $items = OrderItem::factory()->count(2)->state([
        'quantity' => 2,
        'price' => 15000,
    ]);

    $this->order = Order::factory()
        ->for($this->clinic, 'clinic')
        ->has($items, 'items')
        ->create();
});

test('download checklist', function () {
    Storage::fake('local');

    $response = $this->actingAs($this->user)
        ->get($this->order->download_checklist_url)
        ->assertDownload();

    Storage::disk('local')->assertExists("{$this->order->id}-checklist.csv");
});
