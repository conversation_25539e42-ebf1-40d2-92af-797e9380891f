<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Exceptions\VendorServiceError;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\SubOrder;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Events\OrderPlacementFailed;
use App\Modules\Order\Jobs\Vendor\PlaceOrderToVendor;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Exceptions\OrderValidationException;
use App\Modules\Order\Services\Vendor\Factory;
use Illuminate\Support\Facades\Event;

describe('PlaceOrderToVendor job', function () {
    beforeEach(function () {
        $this->vendor = Vendor::factory()->create(['name' => 'Test Vendor']);
        $this->clinic = Clinic::factory()->create(['name' => 'Test Clinic']);
        $this->order = Order::factory()->for($this->clinic)->create(['order_number' => 'HFO123456']);
        $this->suborder = SubOrder::factory()->create([
            'order_id' => $this->order->id,
            'vendor_id' => $this->vendor->id,
        ]);

        IntegrationConnection::factory()->create([
            'vendor_id' => $this->vendor->id,
            'clinic_id' => $this->clinic->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['test' => 'credentials'],
        ]);

        OrderItem::factory()->create([
            'order_id' => $this->order->id,
            'product_offer_id' => ProductOffer::factory()->for($this->vendor)->create()->id,
        ]);
    });

    it('updates suborder with error when vendor exception occurs', function () {
        $mockService = Mockery::mock(OrderProcessor::class);
        $mockService->shouldReceive('validateOrder')
            ->andThrow(new OrderValidationException('Test error', ['test' => 'context']));

        Mockery::mock('alias:'.Factory::class)
            ->shouldReceive('make')
            ->andReturn($mockService);

        $job = new PlaceOrderToVendor($this->suborder);
        $job->handle();

        expect($this->suborder->fresh()->error_message)->toContain('Test error');
        expect($this->suborder->fresh()->items()->first()->status)->toBe(OrderItemStatus::PlacementFailed);
    });

    it('updates suborder with error when vendor service error occurs', function () {
        $mockService = Mockery::mock(OrderProcessor::class);
        $mockService->shouldReceive('validateOrder')
            ->andThrow(new VendorServiceError('Service error'));

        Mockery::mock('alias:'.Factory::class)
            ->shouldReceive('make')
            ->andReturn($mockService);

        $job = new PlaceOrderToVendor($this->suborder);
        $job->handle();

        expect($this->suborder->fresh()->error_message)->toContain('Service error');
        expect($this->suborder->fresh()->items()->first()->status)->toBe(OrderItemStatus::PlacementFailed);
    });

    it('updates suborder with error and re-throws when unexpected error occurs', function () {
        $mockService = Mockery::mock(OrderProcessor::class);
        $mockService->shouldReceive('validateOrder')
            ->andThrow(new Exception('Unexpected error'));

        Mockery::mock('alias:'.Factory::class)
            ->shouldReceive('make')
            ->andReturn($mockService);

        $job = new PlaceOrderToVendor($this->suborder);

        expect(fn () => $job->handle())->toThrow(VendorServiceError::class);

        expect($this->suborder->fresh()->error_message)->toContain('Unexpected error');
        expect($this->suborder->fresh()->items()->first()->status)->toBe(OrderItemStatus::PlacementFailed);
    });

    it('dispatches OrderPlacementFailed event on job failure', function () {
        Event::spy();

        $job = new PlaceOrderToVendor($this->suborder);
        $job->failed(new Exception('Unexpected error'));

        Event::assertDispatched(OrderPlacementFailed::class, function ($event) {
            return $event->suborder->is($this->suborder);
        });
    });

    it('falls back to UNKNOWN_ERROR if error code is invalid', function () {
        $mockService = Mockery::mock(OrderProcessor::class);
        // Simulate an exception with an invalid error code
        $invalidError = new class('Invalid error') extends Exception
        {
            public $errorCode = 'NOT_A_VALID_CODE';

            public $context = ['foo' => 'bar'];
        };
        $mockService->shouldReceive('validateOrder')
            ->andThrow($invalidError);

        Mockery::mock('alias:'.Factory::class)
            ->shouldReceive('make')
            ->andReturn($mockService);

        $job = new PlaceOrderToVendor($this->suborder);

        try {
            $job->handle();
            $this->fail('Expected VendorServiceError was not thrown');
        } catch (VendorServiceError $e) {
            // Should fallback to UNKNOWN_ERROR
            expect($this->suborder->fresh()->error_code)->toBe('UNKNOWN_ERROR');
            expect($this->suborder->fresh()->error_message)->toContain('Invalid error');
        }
    });
});
