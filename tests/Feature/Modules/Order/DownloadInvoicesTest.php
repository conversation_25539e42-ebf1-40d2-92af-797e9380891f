<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\SubOrder;
use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use STS\ZipStream\Facades\Zip;

beforeEach(function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $this->order = Order::factory()
        ->for($this->clinic, 'clinic')
        ->create([
            'created_at' => Carbon::parse('2025-01-01'),
        ]);

    $subOrder = SubOrder::factory()
        ->for($this->order, 'order')
        ->create();

    ExternalOrder::factory()
        ->for($subOrder, 'subOrder')
        ->create([
            'invoice_file_path' => '/test-id/invoices/testfile.txt',
        ]);
});

test('download invoices', function () {
    Storage::shouldReceive('disk')
        ->with('local')
        ->andReturn(Storage::getFacadeRoot());

    Storage::shouldReceive('exists')
        ->with('/test-id/invoices/testfile.txt')
        ->andReturn(true);

    Storage::shouldReceive('path')
        ->with('/test-id/invoices/testfile.txt')
        ->andReturn('tmp/test-id/invoices/testfile.txt');

    Zip::expects('create')
        ->with("{$this->order->id}-invoices.zip", ['tmp/test-id/invoices/testfile.txt']);

    $this->actingAs($this->user)->get($this->order->download_invoices_url);
});
