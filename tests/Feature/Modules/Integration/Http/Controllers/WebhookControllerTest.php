<?php

declare(strict_types=1);

namespace Tests\Feature\Modules\Integration\Http\Controllers;

use App\Enums\WebhookRequestStatus;
use App\Models\Vendor;
use App\Models\WebhookRequest;
use App\Modules\Integration\Contracts\WebhookHandler;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use InvalidArgumentException;
use Tests\TestCase;

final class WebhookControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_processes_valid_webhook_successfully(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create([
            'key' => 'test-vendor',
            'authentication_configuration' => [
                'webhook_handler' => TestSuccessWebhookHandler::class,
            ],
        ]);

        $payload = ['order_id' => '123', 'status' => 'completed'];
        $headers = ['X-Webhook-Signature' => ['valid-signature']];
        $method = 'POST';

        // Act
        $response = $this->postJson(
            '/api/webhook/test-vendor/order.created',
            $payload,
            $headers
        );

        // Assert
        $response->assertStatus(JsonResponse::HTTP_OK)
            ->assertJson(['message' => 'Webhook processed successfully']);

        $this->assertDatabaseHas('webhook_requests', [
            'vendor_id' => $vendor->id,
            'slug' => 'order.created',
            'status' => WebhookRequestStatus::Completed->value,
            'method' => $method,
        ]);

        $webhookRequest = WebhookRequest::where('vendor_id', $vendor->id)->where('slug', 'order.created')->first();
        $this->assertNotNull($webhookRequest);
        $this->assertArrayHasKey('x-webhook-signature', $webhookRequest->headers);
        $this->assertEquals(['valid-signature'], $webhookRequest->headers['x-webhook-signature']);
        $this->assertEquals($payload, $webhookRequest->payload);
        $this->assertEquals(WebhookRequestStatus::Completed, $webhookRequest->status);
    }

    public function test_it_handles_invalid_vendor_key(): void
    {
        // Arrange
        $payload = ['order_id' => '123'];
        $headers = ['X-Webhook-Signature' => ['valid-signature']];
        $method = 'POST';

        // Act
        $response = $this->postJson(
            '/api/webhook/invalid-vendor/order.created',
            $payload,
            $headers
        );

        // Assert
        $response->assertStatus(JsonResponse::HTTP_NOT_FOUND)
            ->assertJson(['error' => 'No vendor found for key: invalid-vendor']);
    }

    public function test_it_handles_invalid_webhook_signature(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create([
            'key' => 'test-vendor',
            'authentication_configuration' => [
                'webhook_handler' => TestInvalidSignatureWebhookHandler::class,
            ],
        ]);

        $payload = ['order_id' => '123'];
        $headers = ['X-Webhook-Signature' => ['invalid-signature']];
        $method = 'POST';

        // Act
        $response = $this->postJson(
            '/api/webhook/test-vendor/order.created',
            $payload,
            $headers
        );

        // Assert
        $response->assertStatus(JsonResponse::HTTP_UNAUTHORIZED)
            ->assertJson(['error' => 'Invalid webhook signature']);

        $this->assertDatabaseHas('webhook_requests', [
            'vendor_id' => $vendor->id,
            'slug' => 'order.created',
            'status' => WebhookRequestStatus::Failed->value,
            'method' => $method,
        ]);
    }

    public function test_it_handles_unexpected_errors(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create([
            'key' => 'test-vendor',
            'authentication_configuration' => [
                'webhook_handler' => TestErrorWebhookHandler::class,
            ],
        ]);

        $payload = ['order_id' => '123'];
        $headers = ['X-Webhook-Signature' => ['valid-signature']];
        $method = 'POST';

        // Act
        $response = $this->postJson(
            '/api/webhook/test-vendor/order.created',
            $payload,
            $headers
        );

        // Assert
        $response->assertStatus(JsonResponse::HTTP_INTERNAL_SERVER_ERROR)
            ->assertJson(['error' => 'Internal server error']);

        $this->assertDatabaseHas('webhook_requests', [
            'vendor_id' => $vendor->id,
            'slug' => 'order.created',
            'status' => WebhookRequestStatus::Failed->value,
            'method' => $method,
        ]);
    }

    public function test_it_handles_invalid_argument_exception(): void
    {
        // Arrange
        $vendor = Vendor::factory()->create([
            'key' => 'test-vendor',
            'authentication_configuration' => [
                'webhook_handler' => TestInvalidArgumentWebhookHandler::class,
            ],
        ]);

        $payload = ['order_id' => '123'];
        $headers = ['X-Webhook-Signature' => ['valid-signature']];
        $method = 'POST';

        // Act
        $response = $this->postJson(
            '/api/webhook/test-vendor/unsupported.event',
            $payload,
            $headers
        );

        // Assert
        $response->assertStatus(JsonResponse::HTTP_BAD_REQUEST)
            ->assertJson(['error' => 'Unsupported webhook event']);

        $this->assertDatabaseHas('webhook_requests', [
            'vendor_id' => $vendor->id,
            'slug' => 'unsupported.event',
            'status' => WebhookRequestStatus::Failed->value,
            'method' => $method,
        ]);
    }
}

final class TestSuccessWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        // Test implementation - success case
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        return true;
    }
}

final class TestInvalidSignatureWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        // Test implementation - should not be called
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        return false;
    }
}

final class TestErrorWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        throw new Exception('Test error');
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        return true;
    }
}

final class TestInvalidArgumentWebhookHandler implements WebhookHandler
{
    public function handle(WebhookRequest $webhookRequest): void
    {
        throw new InvalidArgumentException('Unsupported webhook event');
    }

    public function validateWebhook(array $headers, string $payload): bool
    {
        return true;
    }
}
