<?php

declare(strict_types=1);

use App\Models\SubOrder;
use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Models\IntegrationEvent;
use App\Modules\Integration\Models\IntegrationSession;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Integration Logging', function () {
    it('can log a success event to a session', function () {
        $session = IntegrationSession::factory()->create();
        $subject = SubOrder::factory()->create();
        $event = $session->logSuccessEvent('test_action', $subject, ['foo' => 'bar']);

        expect($event)->toBeInstanceOf(IntegrationEvent::class)
            ->and($event->status)->toBe(IntegrationEventStatus::Success->value)
            ->and($event->action)->toBe('test_action')
            ->and($event->metadata['foo'])->toBe('bar')
            ->and($event->integration_session_id)->toBe($session->id);
    });

    it('can log an error event to a session', function () {
        $session = IntegrationSession::factory()->create();
        $subject = SubOrder::factory()->create();
        $event = $session->logErrorEvent('fail_action', $subject, ['error' => 'something went wrong']);

        expect($event->status)->toBe(IntegrationEventStatus::Error->value)
            ->and($event->action)->toBe('fail_action')
            ->and($event->metadata['error'])->toBe('something went wrong')
            ->and($event->integration_session_id)->toBe($session->id);
    });

    it('relates events to a subject model if provided', function () {
        $session = IntegrationSession::factory()->create();
        $subject = SubOrder::factory()->create();
        $event = $session->logSuccessEvent('with_subject', $subject, []);

        expect($event->subject_id)->toBe($subject->id)
            ->and($event->subject_type)->toBe(get_class($subject));
    });

    it('stores metadata as expected', function () {
        $session = IntegrationSession::factory()->create();
        $subject = SubOrder::factory()->create();
        $meta = [
            'endpoint' => '/api/test',
            'method' => 'POST',
            'body' => ['foo' => 'bar'],
        ];
        $event = $session->logSuccessEvent('meta_test', $subject, $meta);
        expect($event->metadata)->toBe($meta);
    });
});
