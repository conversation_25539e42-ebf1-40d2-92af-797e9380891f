<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Mails\NotifyIntegrationDisconnected;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;

beforeEach(function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $this->vendor = Vendor::factory()->enabled()->create();
});

test('connect integration', function () {
    Bus::fake();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->postJson("/api/integrations/{$this->vendor->id}/connect", [
            'buyingGroupId' => 'test-group-id',
        ]);

    $response->assertCreated()
        ->assertJsonStructure([
            'status',
            'redirectUri',
        ]);

    expect($this->clinic->fresh()->vendors()->first()->integrationConnections()->first()->credentials['buying_group_id'])->toBe('test-group-id');
});

test('disconnect integration', function () {
    Bus::fake();
    Queue::fake();
    Mail::fake();

    $account = ClinicAccount::factory()->create();
    $user = User::factory()->for($account, 'account')->create();
    $vendor = Vendor::factory()->create(['name' => 'Test Vendor']);
    $clinic = Clinic::factory()->for($account, 'account')->create(['name' => 'Test Clinic']);
    $connection = IntegrationConnection::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['test' => 'credentials'],
    ]);

    $connection->status = IntegrationConnectionStatus::Disconnected;
    $connection->save();

    Mail::assertQueued(NotifyIntegrationDisconnected::class);
});
