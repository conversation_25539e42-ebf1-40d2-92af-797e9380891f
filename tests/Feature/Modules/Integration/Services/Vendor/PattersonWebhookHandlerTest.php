<?php

declare(strict_types=1);

namespace Tests\Feature\Modules\Integration\Services\Vendor;

use App\Enums\WebhookRequestStatus;
use App\Models\Vendor;
use App\Models\WebhookRequest;
use App\Modules\Integration\Services\Vendor\PattersonWebhookHandler;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use Mockery;
use Tests\TestCase;

final class PattersonWebhookHandlerTest extends TestCase
{
    use RefreshDatabase;

    private PattersonWebhookHandler $handler;

    private Vendor $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->handler = new PattersonWebhookHandler();

        $this->vendor = Vendor::factory()->create([
            'key' => 'PATN',
            'name' => 'Patterson',
        ]);
    }

    public function test_it_validates_webhook_always_returns_true(): void
    {
        $payload = json_encode(['order_id' => '123', 'status' => 'created']);
        $headers = ['Some-Header' => ['some-value']];

        $isValid = $this->handler->validateWebhook($headers, $payload);

        $this->assertTrue($isValid);
    }

    public function test_it_validates_webhook_with_empty_headers(): void
    {
        $payload = json_encode(['order_id' => '123', 'status' => 'created']);
        $headers = [];

        $isValid = $this->handler->validateWebhook($headers, $payload);

        $this->assertTrue($isValid);
    }

    public function test_it_handles_order_confirmation_webhook(): void
    {
        $webhookRequest = WebhookRequest::create([
            'vendor_id' => $this->vendor->id,
            'method' => 'POST',
            'slug' => 'order.confirmation',
            'headers' => ['X-Patterson-Signature' => ['test-signature']],
            'payload' => [
                'order_id' => '123',
                'confirmation_number' => 'CONF456',
                'status' => 'confirmed',
            ],
            'status' => WebhookRequestStatus::Pending,
        ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Processing Patterson webhook', Mockery::any());

        Log::shouldReceive('info')
            ->once()
            ->with('Handling Patterson order confirmation webhook', [
                'payload' => [
                    'order_id' => '123',
                    'confirmation_number' => 'CONF456',
                    'status' => 'confirmed',
                ],
            ]);

        $this->handler->handle($webhookRequest);

        // If we get here without exception, the handler worked correctly
        $this->assertTrue(true);
    }

    public function test_it_handles_order_confirmation_webhook_with_xml_content(): void
    {
        $xmlContent = '<?xml version="1.0" encoding="UTF-8"?><order><order_id>XML-123</order_id></order>';

        $webhookRequest = WebhookRequest::create([
            'vendor_id' => $this->vendor->id,
            'method' => 'POST',
            'slug' => 'order.confirmation',
            'headers' => ['Content-Type' => ['application/xml']],
            'payload' => ['xml_content' => $xmlContent],
            'status' => WebhookRequestStatus::Pending,
        ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Processing Patterson webhook', Mockery::any());

        Log::shouldReceive('info')
            ->once()
            ->with('Handling Patterson order confirmation webhook', [
                'payload' => ['xml_content' => $xmlContent],
            ]);

        $this->handler->handle($webhookRequest);

        $this->assertTrue(true);
    }

    public function test_it_throws_exception_for_unsupported_webhook_event(): void
    {
        $webhookRequest = WebhookRequest::create([
            'vendor_id' => $this->vendor->id,
            'method' => 'POST',
            'slug' => 'unsupported.event',
            'headers' => ['X-Patterson-Signature' => ['test-signature']],
            'payload' => ['data' => 'test'],
            'status' => WebhookRequestStatus::Pending,
        ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Processing Patterson webhook', Mockery::any());

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported Patterson webhook event: unsupported.event');

        $this->handler->handle($webhookRequest);
    }
}
