<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Support\Facades\Hash;

beforeEach(function () {
    $this->user = User::factory()->create([
        'password' => Hash::make('current-password'),
    ]);
});

it('updates password when current password is correct', function () {
    $response = $this->actingAs($this->user)
        ->patchJson('/api/user/password', [
            'current_password' => 'current-password',
            'new_password' => 'new-password',
        ]);

    $response->assertStatus(204);

    $this->user->refresh();
    expect(Hash::check('new-password', $this->user->password))->toBeTrue();
});

it('returns error when current password is incorrect', function () {
    $response = $this->actingAs($this->user)
        ->patchJson('/api/user/password', [
            'current_password' => 'wrong-password',
            'new_password' => 'new-password',
        ]);

    $response->assertStatus(422);
    $response->assertExact<PERSON>son([
        'errors' => [
            'currentPassword' => [
                'The current password is incorrect',
            ],
        ],
        'message' => 'The current password is incorrect',
    ]);

    $this->user->refresh();
    expect(Hash::check('current-password', $this->user->password))->toBeTrue();
});
