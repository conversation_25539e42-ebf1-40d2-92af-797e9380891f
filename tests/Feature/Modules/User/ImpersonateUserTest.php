<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Modules\User\Actions\StartImpersonateUserByClinic;
use Illuminate\Testing\TestResponse;

function stopImpersonating(): TestResponse
{
    return test()->postJson('/api/users/stop-impersonating');
}

beforeEach(function () {
    $this->clinic = Clinic::factory()->create();

    $this->admin = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $this->regularUser = User::factory()->create([
        'account_id' => $this->clinic->account->id,
        'email' => '<EMAIL>',
    ]);
});

describe('user impersonation', function () {
    it('allows admin to impersonate a user', function () {
        $this->actingAs($this->admin);
        $startImpersonate = new StartImpersonateUserByClinic();
        $startImpersonate->handle($this->clinic);

        $this->assertDatabaseHas('users', [
            'email' => "hello+system-user-{$this->regularUser->account_id}@highfive.vet",
            'is_system_user' => 1,
        ]);

        $systemUser = $this->regularUser->account->users()->where('email', "hello+system-user-{$this->regularUser->account_id}@highfive.vet")->first();

        // Assert that the admin is impersonating the system user
        $this->assertAuthenticatedAs($systemUser);
    });

    it('allows stopping impersonation and returning to admin account', function () {
        // Start impersonation
        $this->actingAs($this->admin);
        $startImpersonate = new StartImpersonateUserByClinic();
        $startImpersonate->handle($this->clinic);

        // Stop impersonation
        $systemUser = $this->regularUser->account->users()->where('email', "hello+system-user-{$this->regularUser->account_id}@highfive.vet")->first();

        $response = $this->actingAs($systemUser)
            ->getJson('api/users/stop-impersonate');

        $response->assertOk()
            ->assertJson([
                'message' => 'Impersonation stopped',
                'redirectUri' => route('nova.pages.index', ['resource' => 'clinics']),
            ]);
        $this->assertAuthenticatedAs($this->admin);
    });
});
