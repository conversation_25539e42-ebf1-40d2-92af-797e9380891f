<?php

declare(strict_types=1);

namespace Tests\Feature\Modules\Promotion;

use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class BuyXGetYPromotionTest extends TestCase
{
    use RefreshDatabase;

    protected Promotion $promotion;

    protected string $productId;

    protected int $triggerQuantity;

    protected int $freeQuantity;

    protected function setUp(): void
    {
        parent::setUp();

        $vendor = \App\Models\Vendor::factory()->create();
        $gpo = \App\Modules\Gpo\Models\GpoAccount::factory()->create();
        $product = \App\Models\Product::factory()->create();
        $productOffer = \App\Models\ProductOffer::factory()->create(['product_id' => $product->id]);

        $this->promotion = Promotion::create([
            'name' => 'Buy X Get Y Demo Promotion',
            'type' => PromotionType::BuyXGetY,
            'description' => 'Buy one, get one free demo',
            'vendor_id' => $vendor->id,
            'priority' => 1,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
            'status' => 'active',
            'promotionable_type' => get_class($gpo),
            'promotionable_id' => $gpo->id,
        ]);

        // Attach product offer to promotion
        $this->promotion->productOffers()->attach($productOffer->id);

        $rule = $this->promotion->rules()->create(['priority' => 1]);
        $rule->conditions()->create([
            'type' => ConditionType::MinimumQuantity,
            'config' => [
                'quantity' => 3,
            ],
        ]);
        $rule->actions()->create([
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'quantity' => 3,
            ],
        ]);

        $this->productId = $product->id;
        $this->triggerQuantity = 3;
        $this->freeQuantity = 3; // Buy 3 Get 3
    }

    public function test_buy_x_get_y_promotion_engine_logic()
    {
        $promotion = $this->promotion;
        $rule = $promotion->rules()->firstOrFail();
        $condition = $rule->conditions()->firstOrFail();
        $action = $rule->actions()->firstOrFail();

        // Eligible cart - Buy 3, get 1 free
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$this->productId => $this->triggerQuantity]
        );
        $isEligible = $condition->evaluate($context);
        $this->assertTrue($isEligible, 'Cart should be eligible for Buy X Get Y');

        $action->apply($context);
        $this->assertNotEmpty($context->freeProducts, 'Should have free products');

        $freeProduct = $context->freeProducts[0];
        $this->assertEquals($this->productId, $freeProduct['product_id']);
        $this->assertEquals($this->freeQuantity, $freeProduct['quantity']);
        $this->assertEquals('Buy 3 Get 3 promotion', $freeProduct['reason']);
        $this->assertEquals($promotion->id, $freeProduct['promotion_id']);
        $this->assertEquals($rule->id, $freeProduct['rule_id']);
        $this->assertEquals($this->productId, $freeProduct['qualifying_product_id']);

        // Test multiple sets - Buy 6, get 6 free (2 sets of 3)
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$this->productId => 6] // 2 sets of 3
        );
        $action->apply($context);
        $this->assertNotEmpty($context->freeProducts);
        $freeProduct = $context->freeProducts[0];
        $this->assertEquals(6, $freeProduct['quantity'], 'Should get 6 free products for buying 6');

        // Ineligible cart - Buy 2, get 0 free (not enough to qualify)
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$this->productId => $this->triggerQuantity - 1]
        );
        $isEligible = $condition->evaluate($context);
        $this->assertFalse($isEligible, 'Cart should NOT be eligible for Buy X Get Y');

        $action->apply($context);
        $this->assertEmpty($context->freeProducts, 'Should not have free products for ineligible cart');
    }

    public function test_buy_x_get_y_promotion_with_multiple_products()
    {
        $vendor = \App\Models\Vendor::factory()->create();
        $gpo = \App\Modules\Gpo\Models\GpoAccount::factory()->create();

        // Create two different products
        $product1 = \App\Models\Product::factory()->create();
        $product2 = \App\Models\Product::factory()->create();
        $productOffer1 = \App\Models\ProductOffer::factory()->create(['product_id' => $product1->id]);
        $productOffer2 = \App\Models\ProductOffer::factory()->create(['product_id' => $product2->id]);

        $promotion = Promotion::create([
            'name' => 'Multi-Product Buy X Get Y',
            'type' => PromotionType::BuyXGetY,
            'description' => 'Buy 3 of any qualifying product, get 1 free',
            'vendor_id' => $vendor->id,
            'priority' => 1,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
            'status' => 'active',
            'promotionable_type' => get_class($gpo),
            'promotionable_id' => $gpo->id,
        ]);

        // Attach both product offers to the promotion
        $promotion->productOffers()->attach([$productOffer1->id, $productOffer2->id]);

        $rule = $promotion->rules()->create(['priority' => 1]);
        $rule->conditions()->create([
            'type' => ConditionType::MinimumQuantity,
            'config' => ['quantity' => 3],
        ]);
        $rule->actions()->create([
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'quantity' => 3,
            ],
        ]);

        // Test: Buy 3 of product1, should get 1 product1 free
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$product1->id => 3]
        );

        $rule->actions()->first()->apply($context);
        $this->assertCount(1, $context->freeProducts);
        $this->assertEquals($product1->id, $context->freeProducts[0]['product_id']);
        $this->assertEquals($product1->id, $context->freeProducts[0]['qualifying_product_id']);

        // Test: Buy 3 of product2, should get 1 product2 free
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$product2->id => 3]
        );

        $rule->actions()->first()->apply($context);
        $this->assertCount(1, $context->freeProducts);
        $this->assertEquals($product2->id, $context->freeProducts[0]['product_id']);
        $this->assertEquals($product2->id, $context->freeProducts[0]['qualifying_product_id']);

        // Test: Buy 3 of both products, should get 2 free products (1 of each)
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$product1->id => 3, $product2->id => 3]
        );

        $rule->actions()->first()->apply($context);
        $this->assertCount(2, $context->freeProducts);

        // Verify we got free products for both qualifying products
        $freeProductIds = array_column($context->freeProducts, 'product_id');
        $this->assertContains($product1->id, $freeProductIds);
        $this->assertContains($product2->id, $freeProductIds);
    }

    public function test_buy_x_get_y_promotion_mix_and_match()
    {
        $vendor = \App\Models\Vendor::factory()->create();
        $gpo = \App\Modules\Gpo\Models\GpoAccount::factory()->create();

        // Create products: buy product1, get product2 free
        $product1 = \App\Models\Product::factory()->create();
        $product2 = \App\Models\Product::factory()->create();
        $productOffer1 = \App\Models\ProductOffer::factory()->create(['product_id' => $product1->id]);
        $productOffer2 = \App\Models\ProductOffer::factory()->create(['product_id' => $product2->id]);

        $promotion = Promotion::create([
            'name' => 'Mix and Match Buy X Get Y',
            'type' => PromotionType::BuyXGetY,
            'description' => 'Buy 2 Product1, get 1 Product2 free',
            'vendor_id' => $vendor->id,
            'priority' => 1,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
            'status' => 'active',
            'promotionable_type' => get_class($gpo),
            'promotionable_id' => $gpo->id,
        ]);

        // Attach only product1 to the promotion (the qualifying product)
        $promotion->productOffers()->attach($productOffer1->id);

        $rule = $promotion->rules()->create(['priority' => 1]);
        $rule->conditions()->create([
            'type' => ConditionType::MinimumQuantity,
            'config' => ['quantity' => 2],
        ]);
        $rule->actions()->create([
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'quantity' => 2,
                'free_product_offer_id' => $productOffer2->id, // Give product2 for free
            ],
        ]);

        // Test: Buy 2 of product1, should get 1 product2 free
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$product1->id => 2]
        );

        $rule->actions()->first()->apply($context);
        $this->assertCount(1, $context->freeProducts);
        $this->assertEquals($product2->id, $context->freeProducts[0]['product_id']);
        $this->assertEquals($product1->id, $context->freeProducts[0]['qualifying_product_id']);
        $this->assertEquals($productOffer2->id, $context->freeProducts[0]['product_offer_id']);
    }

    public function test_buy_x_get_y_promotion_different_quantities()
    {
        $vendor = \App\Models\Vendor::factory()->create();
        $gpo = \App\Modules\Gpo\Models\GpoAccount::factory()->create();
        $product = \App\Models\Product::factory()->create();
        $productOffer = \App\Models\ProductOffer::factory()->create(['product_id' => $product->id]);

        $promotion = Promotion::create([
            'name' => 'Buy 2 Get 3 Promotion',
            'type' => PromotionType::BuyXGetY,
            'description' => 'Buy 2, get 3 free',
            'vendor_id' => $vendor->id,
            'priority' => 1,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
            'status' => 'active',
            'promotionable_type' => get_class($gpo),
            'promotionable_id' => $gpo->id,
        ]);

        $promotion->productOffers()->attach($productOffer->id);

        $rule = $promotion->rules()->create(['priority' => 1]);
        $rule->conditions()->create([
            'type' => ConditionType::MinimumQuantity,
            'config' => ['quantity' => 2],
        ]);
        $rule->actions()->create([
            'type' => ActionType::GiveFreeProduct,
            'config' => [
                'quantity' => 3,
            ],
        ]);

        // Test: Buy 4, should get 6 free (2 sets of buy 2 get 3)
        $context = new Context(
            clinicId: $promotion->promotionable_id,
            cart: [$product->id => 4]
        );

        $rule->actions()->first()->apply($context);
        $this->assertCount(1, $context->freeProducts);
        $this->assertEquals(6, $context->freeProducts[0]['quantity']);
        $this->assertEquals('Buy 2 Get 3 promotion', $context->freeProducts[0]['reason']);
    }
}
