<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\RebateEstimate;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $gpo = GpoAccount::factory()->create();

    $account = ClinicAccount::factory()->for($gpo, 'gpo')->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();
    $this->clinic2 = Clinic::factory()->for($account, 'account')->create();

    $this->vendor = Vendor::factory()->enabled()->create();

    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();
    $this->productOffer2 = ProductOffer::factory()->create();
    $this->productOffer3 = ProductOffer::factory()->create();

    $this->promotion = Promotion::factory()
        ->for($this->vendor)
        ->hasAttached($this->productOffer)
        ->hasAttached($this->productOffer2)
        ->hasAttached($this->productOffer3)
        ->create([
            'promotionable_type' => GpoAccount::class,
            'promotionable_id' => $gpo->id,
            'type' => PromotionType::Rebate,
            'status' => PromotionStatus::Active,
            'started_at' => Carbon::today()->subDay(1),
            'ended_at' => Carbon::today()->addDay(1),
        ]);

    $this->estimate = RebateEstimate::factory()
        ->for($this->clinic)
        ->for($this->promotion)
        ->create();
});

test('calculation of estimated rebate amount', function () {
    $this->estimate->update([
        'current_spend_amount' => 12345,
        'current_rebate_percent' => 10,
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'id' => $this->estimate->id,
            'promotion' => [
                'id' => $this->promotion->id,
            ],
            'estimatedRebateAmount' => '12.34',
        ]]);
});

test('current tier is the last tier', function () {
    $this->estimate->update([
        'current_spend_amount' => 25000,
        'current_rebate_percent' => 15,
        'next_tier_minimum_spend_amount_threshold' => null,
        'next_tier_rebate_percent' => null,
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'id' => $this->estimate->id,
            'isLastTier' => true,
        ]]);
});

test('suggested product offer returns up to 3 product offers that clinic has purchased', function () {
    $productOffer4 = ProductOffer::factory()->for($this->vendor)->create();

    $this->promotion->productOffers()->attach($productOffer4);

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 1,
            'price' => 1000,
        ]), 'items')
        ->create();

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer2->id,
            'quantity' => 1,
            'price' => 1500,
        ]), 'items')
        ->create();

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer3->id,
            'quantity' => 1,
            'price' => 2000,
        ]), 'items')
        ->create();

    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'suggestedProductOffers' => [
                [
                    'id' => $this->productOffer3->id,
                ],
                [
                    'id' => $this->productOffer2->id,
                ],
                [
                    'id' => $this->productOffer->id,
                ],
            ],
        ]]);
});

test('suggested product offers with no order history returns empty array', function () {
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'suggestedProductOffers' => [],
        ]]);
});

test('suggested product offer returns current clinic spend amount', function () {
    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer3->id,
            'quantity' => 3,
            'price' => 2000,
        ]), 'items')
        ->create();

    Order::factory()
        ->for($this->clinic2, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 3,
            'price' => 1000,
        ]), 'items')
        ->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'suggestedProductOffers' => [
                [
                    'id' => $this->productOffer3->id,
                    'orderItemsSumTotalPrice' => 6000,
                ],
            ],
        ]]);
});

test('suggested product offers with order history ordered by total spend amount', function () {
    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 3,
            'price' => 1000,
        ]), 'items')
        ->create();

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer2->id,
            'quantity' => 3,
            'price' => 2000,
        ]), 'items')
        ->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'suggestedProductOffers' => [
                [
                    'id' => $this->productOffer2->id,
                ],
                [
                    'id' => $this->productOffer->id,
                ],
            ],
        ]]);
});

test('suggested product offer only includes orders from the last 12 months', function () {
    $productOffer4 = ProductOffer::factory()->create();

    $this->promotion->productOffers()->attach($productOffer4);

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $productOffer4->id,
            'quantity' => 3,
            'price' => 2000,
        ]), 'items')
        ->state([
            'created_at' => Carbon::today()->subMonths(13),
        ])
        ->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'suggestedProductOffers' => [],
        ]]);
});

test('suggested product offers only includes products that clinic has purchased', function () {
    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 2,
            'price' => 1500,
        ]), 'items')
        ->create();

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has(OrderItem::factory()->state([
            'product_offer_id' => $this->productOffer3->id,
            'quantity' => 1,
            'price' => 3000,
        ]), 'items')
        ->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/rebate-estimates');

    $response->assertOk()
        ->assertJson([[
            'suggestedProductOffers' => [
                [
                    'id' => $this->productOffer->id,
                ],
                [
                    'id' => $this->productOffer3->id,
                ],
            ],
        ]])
        ->assertJsonMissing([[
            'suggestedProductOffers' => [
                [
                    'id' => $this->productOffer2->id,
                ],
            ],
        ]]);
});
