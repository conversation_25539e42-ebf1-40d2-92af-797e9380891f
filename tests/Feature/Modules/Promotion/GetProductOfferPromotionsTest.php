<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $gpo = GpoAccount::factory()->create();

    $account = ClinicAccount::factory()->for($gpo, 'gpo')->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $this->vendor = Vendor::factory()->enabled()->create();

    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->promotion = Promotion::factory()
        ->for($gpo, 'promotionable')
        ->for($this->vendor)
        ->hasAttached($this->productOffer)
        ->create([
            'type' => PromotionType::Rebate,
            'status' => PromotionStatus::Active,
            'started_at' => Carbon::today()->subDay(1),
            'ended_at' => Carbon::today()->addDay(1),
        ]);
});

test('api contract', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/promotions?filter[product_offer_id]={$this->productOffer->id}");

    $response->assertOk()
        ->assertJsonStructure([
            '*' => [
                'id',
                'name',
                'type',
                'description',
            ],
        ]);
});

test('filter non-active promotions', function (PromotionStatus $status) {
    $this->promotion->update(['status' => $status]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/promotions?filter[product_offer_id]={$this->productOffer->id}");

    $response->assertOk()
        ->assertJsonCount(0);
})->with([PromotionStatus::Inactive, PromotionStatus::Draft]);

test('filter promotions by clinic association', function () {
    $gpo = GpoAccount::factory()->create();

    $this->promotion->update(['promotionable_id' => $gpo->id]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/promotions?filter[product_offer_id]={$this->productOffer->id}");

    $response->assertOk()
        ->assertJsonCount(0);
});

test('filter ended promotions', function () {
    $this->promotion->update([
        'started_at' => Carbon::today()->subDays(3),
        'ended_at' => Carbon::today()->subDays(1),
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/promotions?filter[product_offer_id]={$this->productOffer->id}");

    $response->assertOk()
        ->assertJsonCount(0);
});

test('filter future promotions', function () {
    $this->promotion->update([
        'started_at' => Carbon::today()->addDays(1),
        'ended_at' => Carbon::today()->addDays(3),
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/promotions?filter[product_offer_id]={$this->productOffer->id}");

    $response->assertOk()
        ->assertJsonCount(0);
});
