<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\RebateEstimate;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $gpo = GpoAccount::factory()->create();

    $account = ClinicAccount::factory()->for($gpo, 'gpo')->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();
    $this->clinic2 = Clinic::factory()->for($account, 'account')->create();

    $this->vendor = Vendor::factory()->enabled()->create();

    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();
    $this->productOffer2 = ProductOffer::factory()->create();
    $this->productOffer3 = ProductOffer::factory()->create();

    $this->promotion = Promotion::factory()
        ->for($this->vendor)
        ->hasAttached($this->productOffer)
        ->hasAttached($this->productOffer2)
        ->hasAttached($this->productOffer3)
        ->create([
            'promotionable_type' => GpoAccount::class,
            'promotionable_id' => $gpo->id,
            'type' => PromotionType::Rebate,
            'status' => PromotionStatus::Active,
            'started_at' => Carbon::today()->subDay(1),
            'ended_at' => Carbon::today()->addDay(1),
        ]);

    $this->estimate = RebateEstimate::factory()
        ->for($this->clinic)
        ->for($this->promotion)
        ->create();
});

test('get promotion by id', function () {
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson("/api/promotions/{$this->promotion->id}");

    $response->assertOk()
        ->assertJsonStructure([
            'id',
            'name',
            'type',
            'description',
            'startedAt',
            'endedAt',
            'vendor' => [
                'id',
                'name',
                'imageUrl',
            ],
            'offers',
        ])
        ->assertJson([
            'id' => $this->promotion->id,
            'name' => $this->promotion->name,
            'type' => $this->promotion->type->value,
            'description' => $this->promotion->description,
            'startedAt' => $this->promotion->started_at->format('Y-m-d'),
            'endedAt' => $this->promotion->ended_at->format('Y-m-d'),
            'vendor' => [
                'id' => $this->vendor->id,
                'name' => $this->vendor->name,
                'imageUrl' => $this->vendor->image_url,
            ],
        ]);

    $response->assertJsonCount(3, 'offers');
});
