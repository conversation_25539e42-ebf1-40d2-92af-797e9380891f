<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\CatalogSync\Actions\CreateCatalogSyncTask;
use App\Modules\CatalogSync\Actions\StartCatalogSync;
use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Models\IntegrationConnection;

describe('create catalog sync', function () {
    it('creates catalog sync', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create([
            'integration_points' => [IntegrationPoint::SyncProductCatalog],
        ]);

        $fetch = $this->mock(FetchClient::class);

        $fetch->shouldReceive('startCatalogSync')
            ->once();

        $fetch->shouldReceive('getCatalogSyncTaskStatus')
            ->once()
            ->andReturn(CatalogSyncTaskStatus::Pending);

        $startCatalogSync = new StartCatalogSync($fetch);
        $action = new CreateCatalogSyncTask($startCatalogSync);

        IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $action->handle($clinic->id, $vendor->id);

        $catalogSyncTask = CatalogSyncTask::query()->first();

        expect($catalogSyncTask->clinic->id)->toBe($clinic->id);
        expect($catalogSyncTask->vendor->id)->toBe($vendor->id);
        expect($catalogSyncTask->scheduled_at)->not->toBeNull();
    });

    it('starts catalog sync', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create([
            'integration_points' => [IntegrationPoint::SyncProductCatalog],
        ]);

        IntegrationConnection::create([
            'vendor_id' => $vendor->id,
            'clinic_id' => $clinic->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['access_token' => 'token'],
        ]);

        $fetch = $this->mock(FetchClient::class);
        $startCatalogSync = new StartCatalogSync($fetch);
        $action = new CreateCatalogSyncTask($startCatalogSync);

        $fetch->shouldReceive('startCatalogSync')
            ->once();

        $fetch->shouldReceive('getCatalogSyncTaskStatus')
            ->once()
            ->andReturn(CatalogSyncTaskStatus::Pending);

        $action->handle($clinic->id, $vendor->id);
    });

    it('does not create catalog sync if vendor does not support sync product catalog', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        IntegrationConnection::create([
            'vendor_id' => $vendor->id,
            'clinic_id' => $clinic->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['access_token' => 'token'],
        ]);

        $fetch = $this->mock(FetchClient::class);
        $startCatalogSync = new StartCatalogSync($fetch);
        $action = new CreateCatalogSyncTask($startCatalogSync);

        $fetch->shouldNotReceive('startCatalogSync');

        $action->handle($clinic->id, $vendor->id);

        expect(CatalogSyncTask::query()->count())->toBe(0);
    });
});
