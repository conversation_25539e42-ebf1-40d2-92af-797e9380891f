<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\CatalogSync\Actions\StartCatalogSync;
use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Exceptions\StartCatalogSyncException;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

describe('start catalog sync', function () {
    it('starts the catalog sync', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $task = CatalogSyncTask::factory()
            ->create([
                'integration_connection_id' => $connection->id,
            ]);

        $fetch = $this->mock(FetchClient::class);

        $fetch->shouldReceive('startCatalogSync')
            ->once()
            ->with($task->id, $vendor->slug, ['username' => 'test', 'password' => 'test']);

        $action = new StartCatalogSync($fetch);

        $action->handle($task);

        expect($task->status)->toBe(CatalogSyncTaskStatus::Running);
    });

    it('fails when the clinic is not connected to the vendor', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'credentials' => ['username' => 'test', 'password' => 'test'],
            'status' => IntegrationConnectionStatus::Disconnected,
        ]);

        $task = CatalogSyncTask::factory()->create([
            'integration_connection_id' => $connection->id,
        ]);

        $fetch = $this->spy(FetchClient::class);

        $action = new StartCatalogSync($fetch);

        $action->handle($task);

        expect($task->status)->toBe(CatalogSyncTaskStatus::Failed);
        expect($task->status_reason)->toBe('The clinic does not have an active connection with the vendor');
    });

    it('fails when the fetch client fails to start the catalog sync', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $task = CatalogSyncTask::factory()
            ->create([
                'integration_connection_id' => $connection->id,
            ]);

        $fetch = $this->mock(FetchClient::class);

        $fetch->shouldReceive('startCatalogSync')
            ->once()
            ->with($task->id, $vendor->slug, ['username' => 'test', 'password' => 'test'])
            ->andThrow(new StartCatalogSyncException('Something went wrong'));

        $action = new StartCatalogSync($fetch);

        $action->handle($task);

        expect($task->status)->toBe(CatalogSyncTaskStatus::Failed);
        expect($task->status_reason)->toBe('Something went wrong');
    });
});
