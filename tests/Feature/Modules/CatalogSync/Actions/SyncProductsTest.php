<?php

declare(strict_types=1);

use App\Enums\ProductStockStatus;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\CatalogSync\Actions\SyncProduct;
use App\Modules\CatalogSync\Data\ProductOffer as DataProductOffer;

use function PHPUnit\Framework\assertNotNull;

describe('sync products', function () {
    it('creates a new product when the product does not exist', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $data = DataProductOffer::from([
            'id' => '123456789',
            'sku' => '123-456-789',
            'name' => 'Autoclave Steam Indicator Tape 1"',
            'image' => 'https://placehold.co/400x400',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 199,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
        ]);

        $action = new SyncProduct();

        $action->handle($clinic, $vendor, $data);

        $productOffer = ProductOffer::query()->first();

        expect($productOffer->vendor_sku)->toBe('123-456-789');
        expect($productOffer->name)->toBe('Autoclave Steam Indicator Tape 1"');
        expect($productOffer->product->image_url)->toBe('https://placehold.co/400x400');
        expect($productOffer->price)->toBeNull();
        expect($productOffer->stock_status)->toBe(ProductStockStatus::InStock);
        expect($productOffer->last_synced_at)->not->toBeNull();
    });

    it('updates a product when the product exists', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $productOffer = ProductOffer::factory()
            ->for($vendor)
            ->create([
                'vendor_sku' => '123-456-789',
            ]);

        expect($productOffer->name)->not->toBe('Autoclave Steam Indicator Tape 1"');
        assertNotNull($productOffer->last_synced_at);

        $data = DataProductOffer::from([
            'id' => '123456789',
            'sku' => '123-456-789',
            'name' => 'Autoclave Steam Indicator Tape 1"',
            'image' => 'https://placehold.co/400x400',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 199,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
        ]);

        $action = new SyncProduct();

        $action->handle($clinic, $vendor, $data);

        $productOffer->refresh();

        expect($productOffer->name)->toBe('Autoclave Steam Indicator Tape 1"');
        expect($productOffer->last_synced_at)->not->toBeNull();
    });

    it('syncs the product attributes', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $productOffer = ProductOffer::factory()
            ->for($vendor)
            ->create(['vendor_sku' => '123-456-789']);

        $data = DataProductOffer::from([
            'id' => '123456789',
            'sku' => '123-456-789',
            'name' => 'Autoclave Steam Indicator Tape 1"',
            'image' => 'https://placehold.co/400x400',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 199,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
            'attributes' => [
                'Unit of Measure' => 'Each',
            ],
        ]);

        $action = new SyncProduct();

        $action->handle($clinic, $vendor, $data);

        expect($productOffer->product->attributes()->count())->toBe(1);
        expect($productOffer->product->attributes()->where('name', 'Unit of Measure')->exists())->toBeTrue();

        $data = DataProductOffer::from([
            ...$data->toArray(),
            'attributes' => [],
        ]);

        $action->handle($clinic, $vendor, $data);

        expect($productOffer->product->attributes()->count())->toBe(0);
    });

    it('syncs the product clinic price', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $product = ProductOffer::factory()
            ->for($vendor)
            ->create(['vendor_sku' => '123-456-789']);

        $product->clinics()->attach($clinic->id, ['price' => 199]);

        $data = DataProductOffer::from([
            'id' => '123456789',
            'sku' => '123-456-789',
            'name' => 'Autoclave Steam Indicator Tape 1"',
            'image' => 'https://placehold.co/400x400',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 299,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
        ]);

        $action = new SyncProduct();

        $action->handle($clinic, $vendor, $data);

        expect($product->clinics()->first()->pivot->price)->toBe(299);

        $data = DataProductOffer::from([
            ...$data->toArray(),
            'pricing' => [
                'clinicPriceInCents' => null,
            ],
        ]);

        $action->handle($clinic, $vendor, $data);

        expect($product->clinics()->count())->toBe(0);
    });

    it('restores the product if it has been deleted', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $product = ProductOffer::factory()
            ->for($vendor)
            ->create(['vendor_sku' => '123-456-789']);

        $product->delete();

        expect($product->trashed())->toBeTrue();

        $data = DataProductOffer::from([
            'id' => '123456789',
            'sku' => '123-456-789',
            'name' => 'Autoclave Steam Indicator Tape 1"',
            'image' => 'https://placehold.co/400x400',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 199,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
        ]);

        $action = new SyncProduct();

        $action->handle($clinic, $vendor, $data);

        expect($product->fresh()->trashed())->toBeFalse();
    });

    it('activates the product if it has been deactivated', function () {
        $clinic = Clinic::factory()->create();
        $vendor = Vendor::factory()->create();

        $product = ProductOffer::factory()
            ->for($vendor)
            ->create([
                'vendor_sku' => '123-456-789',
                'deactivated_at' => now(),
            ]);

        expect($product->deactivated_at)->not->toBeNull();

        $data = DataProductOffer::from([
            'id' => '123456789',
            'sku' => '123-456-789',
            'name' => 'Autoclave Steam Indicator Tape 1"',
            'image' => 'https://placehold.co/400x400',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 199,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
        ]);

        $action = new SyncProduct();

        $action->handle($clinic, $vendor, $data);

        $product->refresh();

        expect($product->deactivated_at)->toBeNull();
    });
});
