<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\CatalogSync\Actions\CreateCatalogSyncBatch;
use App\Modules\CatalogSync\Data\BatchResult;
use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use App\Modules\CatalogSync\Enums\ResultBatchStatus;
use App\Modules\CatalogSync\Jobs\ProcessCatalogSyncBatch;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Queue;

describe('create catalog sync batch', function () {
    it('creates a catalog sync batch from a batch result', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();
        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $task = CatalogSyncTask::factory()->create([
            'integration_connection_id' => $connection->id,
        ]);

        $action = app(CreateCatalogSyncBatch::class);
        $result = new BatchResult($task->id, ResultBatchStatus::Success, null, []);

        Queue::fake();

        $action->handle($result);

        Queue::assertPushed(ProcessCatalogSyncBatch::class);

        $batch = $task->batches()->first();

        expect($batch->status)->toBe(CatalogSyncBatchStatus::Pending);
        expect($batch->message)->toBe($result->toArray());
    });

    it('creates the batch even if the batch result is not successful', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();
        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $task = CatalogSyncTask::factory()->create([
            'integration_connection_id' => $connection->id,
        ]);

        $action = app(CreateCatalogSyncBatch::class);
        $result = new BatchResult($task->id, ResultBatchStatus::Error, 'Something went wrong', []);

        Queue::fake();

        $action->handle($result);

        Queue::assertNotPushed(ProcessCatalogSyncBatch::class);

        $batch = $task->batches()->first();

        expect($batch->message)->toBe($result->toArray());
        expect($batch->status)->toBe(CatalogSyncBatchStatus::Failed);
        expect($batch->status_reason)->toBe('Something went wrong');
    });

    it('fails when the task is not found', function () {
        $action = app(CreateCatalogSyncBatch::class);
        $result = new BatchResult('9f4469cf-0000-0000-0000-000000000000', ResultBatchStatus::Success, null, []);

        $action->handle($result);
    })->throws(ModelNotFoundException::class);

    it('marks the integration connection as disconnected if the batch result is not successful', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();
        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => ['username' => 'test', 'password' => 'test'],
        ]);

        $task = CatalogSyncTask::factory()->create([
            'integration_connection_id' => $connection->id,
        ]);

        $action = app(CreateCatalogSyncBatch::class);
        $result = new BatchResult($task->id, ResultBatchStatus::Error, 'Something went wrong', []);

        $action->handle($result);

        expect($connection->fresh()->status)->toBe(IntegrationConnectionStatus::Disconnected);
    });
});
