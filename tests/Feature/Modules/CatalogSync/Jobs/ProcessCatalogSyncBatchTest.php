<?php

declare(strict_types=1);

namespace Tests\Feature\Modules\CatalogSync\Jobs;

use App\Enums\ProductStockStatus;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\CatalogSync\Contracts\FetchClient;
use App\Modules\CatalogSync\Data\ProductOffer as DataProductOffer;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Enums\ResultBatchStatus;
use App\Modules\CatalogSync\Jobs\ProcessCatalogSyncBatch;
use App\Modules\CatalogSync\Models\CatalogSyncBatch;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

describe('process catalog sync batch', function () {
    it('does not stop processing if a single product fails to sync', function () {
        $invalidProduct = DataProductOffer::empty();

        $validProduct = DataProductOffer::from([
            'id' => '987654321',
            'sku' => '987-654-321',
            'name' => 'A Valid Product',
            'increments' => 1,
            'pricing' => [
                'clinicPriceInCents' => 199,
            ],
            'stockStatus' => ProductStockStatus::InStock,
            'flags' => [],
        ]);

        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        $connection = IntegrationConnection::create([
            'clinic_id' => $clinic->id,
            'vendor_id' => $vendor->id,
            'credentials' => ['username' => 'test', 'password' => 'test'],
            'status' => IntegrationConnectionStatus::Connected,
        ]);

        $task = CatalogSyncTask::factory()->create([
            'integration_connection_id' => $connection->id,
        ]);

        $batch = CatalogSyncBatch::factory()
            ->create([
                'catalog_sync_task_id' => $task->id,
                'message' => [
                    'taskId' => $task->id,
                    'status' => ResultBatchStatus::Success,
                    'statusReason' => null,
                    'products' => [
                        $invalidProduct,
                        $validProduct,
                    ],
                ],
            ]);

        $fetch = $this->mock(FetchClient::class);

        $fetch->shouldReceive('getCatalogSyncTaskStatus')
            ->once()
            ->with($task->id)
            ->andReturn(CatalogSyncTaskStatus::Succeeded);

        ProcessCatalogSyncBatch::dispatch($batch);

        expect(ProductOffer::query()->count())->toBe(1);
        expect($batch->fresh()->success())->toBeTrue();
        expect($task->fresh()->status)->toBe(CatalogSyncTaskStatus::Succeeded);
    });
});
