<?php

declare(strict_types=1);

use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create(['name' => 'Test GPO Account']);

    $this->gpoUser = GpoUser::factory()->create([
        'name' => 'Test GPO User',
        'email' => '<EMAIL>',
        'password' => Hash::make('password123'),
        'account_id' => $this->gpoAccount->id,
    ]);
});

describe('GPO Login', function () {
    it('allows a GPO user to login with valid credentials', function () {
        $response = $this->postJson('/api/gpo/login', [
            'email' => $this->gpoUser->email,
            'password' => 'password123',
        ]);

        $response->assertOk()
            ->assertJsonStructure([
                'access_token',
                'token_type',
                'user' => [
                    'id',
                    'name',
                    'email',
                    'account' => [
                        'id',
                        'name',
                    ],
                ],
            ])
            ->assertJson([
                'token_type' => 'Bearer',
                'user' => [
                    'id' => $this->gpoUser->id,
                    'email' => $this->gpoUser->email,
                    'name' => $this->gpoUser->name,
                    'account' => [
                        'id' => $this->gpoAccount->id,
                        'name' => $this->gpoAccount->name,
                    ],
                ],
            ]);

        expect($response->json('access_token'))->toBeString();
        expect(mb_strlen($response->json('access_token')))->toBeGreaterThan(10);

        // Verify token was created in database
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(1);
    });

    it('rejects login with invalid email', function () {
        $response = $this->postJson('/api/gpo/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['email'])
            ->assertJson([
                'errors' => [
                    'email' => ['The provided credentials are incorrect.'],
                ],
            ]);
    });

    it('rejects login with invalid password', function () {
        $response = $this->postJson('/api/gpo/login', [
            'email' => $this->gpoUser->email,
            'password' => 'wrongpassword',
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['email'])
            ->assertJson([
                'errors' => [
                    'email' => ['The provided credentials are incorrect.'],
                ],
            ]);
    });

    it('validates required email field', function () {
        $response = $this->postJson('/api/gpo/login', [
            'password' => 'password123',
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['email'])
            ->assertJson([
                'errors' => [
                    'email' => ['The email field is required.'],
                ],
            ]);
    });

    it('validates required password field', function () {
        $response = $this->postJson('/api/gpo/login', [
            'email' => $this->gpoUser->email,
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['password'])
            ->assertJson([
                'errors' => [
                    'password' => ['The password field is required.'],
                ],
            ]);
    });

    it('validates email format', function () {
        $response = $this->postJson('/api/gpo/login', [
            'email' => 'invalid-email-format',
            'password' => 'password123',
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['email'])
            ->assertJson([
                'errors' => [
                    'email' => ['The email field must be a valid email address.'],
                ],
            ]);
    });

    it('handles multiple validation errors simultaneously', function () {
        $response = $this->postJson('/api/gpo/login', [
            'email' => 'invalid-email',
            'password' => '',
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['email', 'password']);
    });
});

describe('GPO Authenticated User Endpoint', function () {
    it('returns authenticated user details', function () {
        $token = $this->gpoUser->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->getJson('/api/gpo/user');

        $response->assertOk()
            ->assertJson([
                'id' => $this->gpoUser->id,
                'name' => $this->gpoUser->name,
                'email' => $this->gpoUser->email,
                'account' => [
                    'id' => $this->gpoAccount->id,
                    'name' => $this->gpoAccount->name,
                ],
                'account_id' => $this->gpoAccount->id,
            ])
            ->assertJsonStructure([
                'id',
                'name',
                'email',
                'account' => [
                    'id',
                    'name',
                    'imageUrl',
                ],
                'account_id',
            ]);
    });

    it('requires authentication', function () {
        $response = $this->getJson('/api/gpo/user');

        $response->assertUnauthorized()
            ->assertJson([
                'message' => 'Unauthenticated.',
            ]);
    });

    it('rejects invalid token', function () {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->getJson('/api/gpo/user');

        $response->assertUnauthorized()
            ->assertJson([
                'message' => 'Unauthenticated.',
            ]);
    });

    it('rejects malformed authorization header', function () {
        $response = $this->withHeaders([
            'Authorization' => 'InvalidFormat token',
        ])->getJson('/api/gpo/user');

        $response->assertUnauthorized()
            ->assertJson([
                'message' => 'Unauthenticated.',
            ]);
    });
});

describe('GPO Logout', function () {
    it('successfully logs out authenticated user', function () {
        $token = $this->gpoUser->createToken('test-token')->plainTextToken;

        // Verify token exists
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(1);

        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->postJson('/api/gpo/logout');

        $response->assertOk()
            ->assertJson([
                'message' => 'Successfully logged out',
            ]);

        // Verify token was deleted
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(0);
    });

    it('requires authentication for logout', function () {
        $response = $this->postJson('/api/gpo/logout');

        $response->assertUnauthorized()
            ->assertJson([
                'message' => 'Unauthenticated.',
            ]);
    });

    it('revokes token successfully on logout', function () {
        $token = $this->gpoUser->createToken('test-token')->plainTextToken;

        // Verify token exists
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(1);

        // Logout should succeed
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->postJson('/api/gpo/logout');

        $response->assertOk()
            ->assertJson([
                'message' => 'Successfully logged out',
            ]);

        // Verify token was deleted
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(0);
    });

    it('only revokes current token when user has multiple tokens', function () {
        $token1 = $this->gpoUser->createToken('token-1')->plainTextToken;
        $token2 = $this->gpoUser->createToken('token-2')->plainTextToken;

        // Verify both tokens exist
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(2);

        // Logout with first token
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token1",
        ])->postJson('/api/gpo/logout');

        $response->assertOk();

        // Verify only one token was deleted
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(1);

        // Verify second token still works
        $this->withHeaders([
            'Authorization' => "Bearer $token2",
        ])->getJson('/api/gpo/user')
            ->assertOk();
    });
});

describe('GPO Authentication Guard Integration', function () {
    it('correctly identifies GPO guard authentication', function () {
        $token = $this->gpoUser->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->getJson('/api/gpo/user');

        $response->assertOk();

        // Verify the user was authenticated via GPO guard
        expect($this->gpoUser->fresh())->not->toBeNull();
    });

    it('does not authenticate regular users with GPO endpoints', function () {
        // This test ensures GPO endpoints only work with GPO users
        // We would need a regular user token to test this thoroughly
        $token = $this->gpoUser->createToken('test-token')->plainTextToken;

        // This should work since it's a GPO user
        $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->getJson('/api/gpo/user')
            ->assertOk();
    });
});

describe('Token Management', function () {
    it('generates unique tokens for each login', function () {
        // First login
        $response1 = $this->postJson('/api/gpo/login', [
            'email' => $this->gpoUser->email,
            'password' => 'password123',
        ]);

        // Second login
        $response2 = $this->postJson('/api/gpo/login', [
            'email' => $this->gpoUser->email,
            'password' => 'password123',
        ]);

        $token1 = $response1->json('access_token');
        $token2 = $response2->json('access_token');

        expect($token1)->not->toBe($token2);

        // Both tokens should work
        $this->withHeaders(['Authorization' => "Bearer $token1"])
            ->getJson('/api/gpo/user')
            ->assertOk();

        $this->withHeaders(['Authorization' => "Bearer $token2"])
            ->getJson('/api/gpo/user')
            ->assertOk();
    });

    it('includes proper token name in database', function () {
        $this->gpoUser->createToken('test-token-name');

        $personalAccessToken = PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->first();

        expect($personalAccessToken->name)->toBe('test-token-name');
        expect($personalAccessToken->tokenable_type)->toBe(GpoUser::class);
    });
});

describe('Full Authentication Flow', function () {
    it('completes full login-access-logout cycle', function () {
        // Step 1: Login
        $loginResponse = $this->postJson('/api/gpo/login', [
            'email' => $this->gpoUser->email,
            'password' => 'password123',
        ]);

        $loginResponse->assertOk();
        $token = $loginResponse->json('access_token');

        // Step 2: Access protected resource
        $userResponse = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->getJson('/api/gpo/user');

        $userResponse->assertOk()
            ->assertJson([
                'email' => $this->gpoUser->email,
            ]);

        // Step 3: Logout
        $logoutResponse = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->postJson('/api/gpo/logout');

        $logoutResponse->assertOk();

        // Step 4: Verify token is revoked by checking database
        expect(PersonalAccessToken::where('tokenable_id', $this->gpoUser->id)->count())->toBe(0);
    });
});
