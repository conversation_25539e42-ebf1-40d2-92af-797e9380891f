<?php

declare(strict_types=1);

use App\Mail\UserRegistered;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccountDetails;
use Illuminate\Support\Facades\Mail;

it('creates a clinic account with correct data', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response
        ->assertCreated()
        ->assertJson([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'roles' => [ClinicAccountRole::Owner->value],
        ])
        ->assertJsonStructure([
            'id',
            'accountId',
        ]);
});

it('creates clinic account details record', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertCreated();

    expect(ClinicAccountDetails::query()->count())->toBe(1);
});

it('sends a welcome email to the user', function () {
    Mail::fake();

    $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'name' => 'Michael Scott',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    Mail::assertQueued(UserRegistered::class, function ($mail) {
        return $mail->user->email === '<EMAIL>';
    });
});
