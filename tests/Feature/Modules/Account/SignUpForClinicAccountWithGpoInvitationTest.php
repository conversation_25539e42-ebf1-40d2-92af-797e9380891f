<?php

declare(strict_types=1);

use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoInvitation;

it('creates a clinic account with GPO invitation', function () {
    /** @var GpoAccount $gpo */
    $gpo = GpoAccount::factory()->create();

    /** @var GpoInvitation $invitation */
    $invitation = GpoInvitation::factory()->for($gpo, 'gpo')->create([
        'email' => '<EMAIL>',
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'invitationToken' => $invitation->token,
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertCreated();

    expect($invitation->refresh()->accepted_at)->not->toBeNull();
    expect($invitation->refresh()->clinic_account_id)->not->toBeNull();
    expect(ClinicAccount::query()->where('gpo_account_id', $invitation->gpo_account_id)->count())->toBe(1);
});

it('rejects invalid invitation tokens', function () {
    /** @var GpoAccount $gpo */
    $gpo = GpoAccount::factory()->create();

    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'invitationToken' => 'invalid-token',
        'name' => 'Michael Scott',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('rejects already accepted invitations', function () {
    /** @var GpoAccount $gpo */
    $gpo = GpoAccount::factory()->create();

    /** @var GpoInvitation $invitation */
    $invitation = GpoInvitation::factory()->for($gpo, 'gpo')->accepted()->create([
        'email' => '<EMAIL>',
    ]);

    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'invitationToken' => $invitation->token,
        'name' => 'Michael Scott',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('rejects expired invitations', function () {
    /** @var GpoInvitation $invitation */
    $invitation = GpoInvitation::factory()->expired()->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'invitationToken' => $invitation->token,
        'name' => 'Michael Scott',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('rejects invitations for other email addresses', function () {
    /** @var GpoInvitation $invitation */
    $invitation = GpoInvitation::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $response = $this->postJson('/api/accounts/sign-up', [
        'accountType' => 'clinic_account',
        'invitationToken' => $invitation->token,
        'name' => 'Michael Scott',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});
