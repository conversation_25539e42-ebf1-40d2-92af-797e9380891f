<?php

declare(strict_types=1);

use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoInvitation;

it('accepts a GPO invitation', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create(['email' => '<EMAIL>']);
    $user->assignRole(ClinicAccountRole::Owner);

    $invitation = GpoInvitation::factory()->create(['email' => $user->email]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
            'invitationToken' => $invitation->token,
        ]);

    $response->assertOk()
        ->assertJson([
            'id' => $account->id,
            'gpo' => [
                'id' => $invitation->gpo->id,
                'name' => $invitation->gpo->name,
            ],
        ]);

    expect($invitation->refresh()->clinic_account_id)->toBe($account->id);
    expect($invitation->accepted_at)->not->toBeNull();
    expect($account->fresh()->gpo_account_id)->toBe($invitation->gpo_account_id);
});

it('rejects invalid invitation tokens', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
            'invitationToken' => 'invalid-token',
        ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('rejects already accepted invitations', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create(['email' => '<EMAIL>']);
    $user->assignRole(ClinicAccountRole::Owner);

    $invitation = GpoInvitation::factory()->accepted()->create(['email' => $user->email]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
            'invitationToken' => $invitation->token,
        ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('rejects expired invitations', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create(['email' => '<EMAIL>']);
    $user->assignRole(ClinicAccountRole::Owner);

    $invitation = GpoInvitation::factory()->expired()->create(['email' => $user->email]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
            'invitationToken' => $invitation->token,
        ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('rejects invitations for other email addresses', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create(['email' => '<EMAIL>']);
    $user->assignRole(ClinicAccountRole::Owner);

    $invitation = GpoInvitation::factory()->create(['email' => '<EMAIL>']);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
            'invitationToken' => $invitation->token,
        ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors('invitationToken');
});

it('blocks unauthenticated users', function () {
    $account = ClinicAccount::factory()->create();
    $invitation = GpoInvitation::factory()->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
        'invitationToken' => $invitation->token,
    ]);

    $response->assertUnauthorized();
});

it('blocks unauthorized users', function () {
    $account = ClinicAccount::factory()->create();
    $invitation = GpoInvitation::factory()->create();

    /**
     * The user is not associated with the account, hence they are unauthorized.
     *
     * @var User $user
     **/
    $user = User::factory()->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/accept-gpo-invitation", [
            'invitationToken' => $invitation->token,
        ]);

    $response->assertForbidden();
});
