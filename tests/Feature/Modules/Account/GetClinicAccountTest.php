<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

it('shows basic account info', function () {
    /** @var ClinicAccount $account */
    $account = ClinicAccount::factory()->create();
    $account->details()->create([
        'ein' => '12-3456789',
        'phone_number' => '************',
    ]);

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->getJson("/api/accounts/{$account->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $account->id,
            'name' => $account->name,
            'ein' => $account->details?->ein,
            'phoneNumber' => $account->details?->phone_number,
            'gpo' => null,
            'clinics' => [],
            'hasVendorConnections' => false,
            /** @deprecated */
            'hasAnyVendorConnected' => false,
        ]);
});

it('lists associated clinics', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    $clinic = Clinic::factory()->for($account, 'account')->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->getJson("/api/accounts/{$account->id}");

    $response->assertOk()
        ->assertJson([
            'clinics' => [
                [
                    'id' => $clinic->id,
                    'name' => $clinic->name,
                    'vendorConnectionsCount' => 0,
                    'hasVendorConnections' => false,
                    /** @deprecated */
                    'hasAnyVendorConnected' => false,
                ],
            ],
        ]);
});

it('shows when a clinic has connected vendors', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    $clinic = Clinic::factory()->for($account, 'account')->create();

    $vendor = Vendor::factory()->enabled()->create();

    IntegrationConnection::create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->getJson("/api/accounts/{$account->id}");

    $response->assertOk()
        ->assertJson([
            'clinics' => [
                [
                    'vendorConnectionsCount' => 1,
                    'hasVendorConnections' => true,
                    /** @deprecated */
                    'hasAnyVendorConnected' => true,
                ],
            ],
            'hasVendorConnections' => true,
            /** @deprecated */
            'hasAnyVendorConnected' => true,
        ]);
});

it('shows gpo info when user is under a gpo', function () {
    $gpo = GpoAccount::factory()->create();

    $account = ClinicAccount::factory()->for($gpo, 'gpo')->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->getJson("/api/accounts/{$account->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $account->id,
            'gpo' => [
                'id' => $gpo->id,
                'name' => $gpo->name,
            ],
        ]);
});

it('shows gpo preffered vendors', function () {
    $gpo = GpoAccount::factory()->create();

    $account = ClinicAccount::factory()->for($gpo, 'gpo')->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    $preferredVendors = Vendor::factory()->count(2)->create();
    $gpo->recommendedVendors()->attach($preferredVendors->pluck('id')->toArray());

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->getJson("/api/accounts/{$account->id}");

    $response->assertOk()
        ->assertJson([
            'id' => $account->id,
            'gpo' => [
                'id' => $gpo->id,
                'name' => $gpo->name,
                'preferredVendors' => $gpo->recommendedVendors->map(function ($vendor) {
                    return [
                        'vendorId' => $vendor->id,
                        'order' => $vendor->pivot->order ?? 0,
                    ];
                })->toArray(),
            ],
        ]);
});

it('blocks unauthenticated users', function () {
    $account = ClinicAccount::factory()->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->getJson("/api/accounts/{$account->id}");

    $response->assertUnauthorized();
});

it('blocks unauthorized users', function () {
    $account = ClinicAccount::factory()->create();

    /**
     * The user is not associated with the account, hence they are unauthorized.
     *
     * @var User $user
     **/
    $user = User::factory()->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->getJson("/api/accounts/{$account->id}");

    $response->assertForbidden();
});
