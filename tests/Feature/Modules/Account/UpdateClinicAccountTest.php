<?php

declare(strict_types=1);

use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;

it('allows owners and admins to update a clinic account', function (ClinicAccountRole $role) {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole($role);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this
        ->actingAs($user)
        ->patchJson("/api/accounts/{$account->id}", [
            'name' => 'Dunder Mifflin',
            'ein' => '12-3456789',
            'phoneNumber' => '************',
        ]);

    $response->assertOk()
        ->assertJson([
            'id' => $account->id,
            'name' => 'Dunder Mifflin',
            'ein' => '12-3456789',
            'phoneNumber' => '************',
        ]);

    expect($account->refresh()->name)->toBe('Dunder Mifflin');
    expect($account->details->ein)->toBe('12-3456789');
    expect($account->details->phone_number)->toBe('************');
})->with([ClinicAccountRole::Owner, ClinicAccountRole::Admin]);

it('blocks managers from updating a clinic account', function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $user */
    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Manager);

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this
        ->actingAs($user)
        ->patchJson("/api/accounts/{$account->id}", [
            'name' => 'Dunder Mifflin',
            'ein' => '12-3456789',
            'phoneNumber' => '************',
        ]);

    $response->assertForbidden();

    $account->refresh();

    expect($account->name)->not->toBe('Dunder Mifflin');
    expect($account->details?->ein ?? null)->not->toBe('12-3456789');
    expect($account->details?->phone_number ?? null)->not->toBe('************');
});

it('blocks unauthenticated users from updating a clinic account', function () {
    $account = ClinicAccount::factory()->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->patchJson("/api/accounts/{$account->id}", [
        'name' => 'Dunder Mifflin',
        'ein' => '12-3456789',
        'phoneNumber' => '************',
    ]);

    $response->assertUnauthorized();

    $account->refresh();

    expect($account->name)->not->toBe('Dunder Mifflin');
    expect($account->details?->ein ?? null)->not->toBe('12-3456789');
    expect($account->details?->phone_number ?? null)->not->toBe('************');
});
