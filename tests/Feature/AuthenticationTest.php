<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use Illuminate\Testing\TestResponse;

function login(string $email, string $password): TestResponse
{
    return test()->postJson('/sessions', [
        'email' => $email,
        'password' => $password,
    ]);
}

function logout(): TestResponse
{
    return test()->deleteJson('/sessions');
}

beforeEach(function () {
    $this->user = User::factory()->create(['email' => '<EMAIL>']);
});

describe('authentication', function () {
    describe('logging in', function () {
        it('allows a registered user to log in successfully', function () {
            login($this->user->email, 'password')
                ->assertCreated()
                ->assertJson(['email' => $this->user->email]);

            $this->assertAuthenticatedAs($this->user);
        });

        it('provides access to protected routes for logged-in users', function () {
            login($this->user->email, 'password');

            $this->getJson('/api/users/me')
                ->assertOk()
                ->assertJson(['email' => $this->user->email]);
        });

        it('directs logged-in users to their account page when logging in again', function () {
            $this->actingAs($this->user);

            login($this->user->email, 'password')
                ->assertRedirect('/api/users/me');
        });

        it('returns clinic id in the response', function () {
            $this->actingAs($this->user);

            $clinic = Clinic::factory()->create();

            $this->user->update(['account_id' => $clinic->clinic_account_id]);

            $this->getJson('/api/users/me')
                ->assertJson(['clinicId' => $clinic->id]);
        });
    });

    describe('logging out', function () {
        it('allows a logged-in user to log out successfully', function () {
            login($this->user->email, 'password');

            logout()->assertNoContent();

            $this->assertGuest();
        });

        it('ensures only logged-in users can access the logout functionality', function () {
            logout()->assertUnauthorized();
        });
    });
});
