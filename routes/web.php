<?php

declare(strict_types=1);

use App\Http\Controllers\SessionController;
use Illuminate\Support\Facades\Route;

// Web routes
Route::post('/sessions', [SessionController::class, 'createUserSession'])->middleware(['guest']);
Route::delete('/sessions', [SessionController::class, 'destroyUserSession'])->middleware(['auth']);

// Gpo routes
Route::post('/gpo/sessions', [SessionController::class, 'createGpoSession'])->middleware(['guest']);
Route::delete('/gpo/sessions', [SessionController::class, 'destroyGpoSession'])->middleware(['auth']);
