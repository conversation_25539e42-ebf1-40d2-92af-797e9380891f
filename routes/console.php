<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schedule;

Schedule::command('horizon:snapshot')->everyFiveMinutes();

Schedule::command('catalog-sync:start')->daily()->environments(['production']);

Schedule::command('product:clean-up-stale')->daily();

Schedule::command('vendor:sync-order')->everySixHours()->environments(['production']);
Schedule::command('vendor:sync-order-shipment')->everySixHours()->environments(['production']);
Schedule::command('vendor:sync-invoices')->everySixHours()->environments(['production']);
Schedule::command('vendor:reconcile-orders')->everySixHours()->environments(['production']);
