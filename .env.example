# Application
APP_NAME="HighFive Vet"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=pgsql
DB_HOST=database
DB_PORT=5432
DB_DATABASE=highfive
DB_USERNAME=highfive
DB_PASSWORD=secret

# Cache & Queue
CACHE_STORE=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="HighFive"

# OpenSearch
OPENSEARCH_HOST=opensearch:9200
SCOUT_DRIVER=opensearch

# Docker Ports
APP_PORT=8000
DB_PORT=5432
REDIS_PORT=6379
OPENSEARCH_PORT=9200
MAILPIT_HTTP_PORT=8025

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=debug 